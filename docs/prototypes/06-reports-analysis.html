<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketTrac - 報表分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 0 auto;
            background: #000;
            border-radius: 25px;
            padding: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .tab-active {
            background: white;
            color: #667eea;
        }
        .tab-inactive {
            background: transparent;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- Status Bar -->
            <div class="status-bar flex justify-between items-center px-4">
                <span class="text-sm font-medium">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-full text-xs"></i>
                </div>
            </div>

            <!-- Header -->
            <div class="gradient-bg text-white px-4 py-4">
                <div class="relative mb-4 flex items-center justify-center">
                    <button class="absolute left-0 text-white">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <h1 class="text-xl font-bold text-center w-full">報表分析</h1>
                </div>

                <!-- 時間選擇器 -->
                <div class="flex bg-white/20 backdrop-blur-sm rounded-lg p-1 mb-4">
                    <button class="flex-1 py-2 rounded-md text-center font-medium text-xs tab-inactive">日</button>
                    <button class="flex-1 py-2 rounded-md text-center font-medium text-xs tab-inactive">週</button>
                    <button class="flex-1 py-2 rounded-md text-center font-medium text-xs tab-active">月</button>
                    <button class="flex-1 py-2 rounded-md text-center font-medium text-xs tab-inactive">年</button>
                </div>

                <!-- 期間位移（上一個 / 下一個） -->
                <div class="flex items-center justify-between mb-4 text-white">
                    <button class="px-3 py-1.5 rounded-md bg-white/20 hover:bg-white/30 transition">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <!-- 依目前粒度顯示期間，預設為 月 粒度示例 -->
                    <span class="text-sm font-medium select-none">2024 年 3 月</span>
                    <button class="px-3 py-1.5 rounded-md bg-white/20 hover:bg-white/30 transition">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <!-- 總覽卡片 -->
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                    <p class="text-white/80 text-sm mb-3">3月 2024 總覽</p>
                    <div class="grid grid-cols-3 gap-3 text-center">
                        <div>
                            <p class="text-xs opacity-80">總收入</p>
                            <p class="text-lg font-bold text-green-300">+25,000</p>
                        </div>
                        <div>
                            <p class="text-xs opacity-80">總支出</p>
                            <p class="text-lg font-bold text-red-300">-18,500</p>
                        </div>
                        <div>
                            <p class="text-xs opacity-80">結餘</p>
                            <p class="text-lg font-bold text-yellow-300">+6,500</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="flex-1 px-4 py-4 pb-20 overflow-y-auto">
                <!-- 圖表切換 -->
                <div class="flex bg-gray-100 rounded-xl p-1 mb-6">
                    <button class="flex-1 py-2 rounded-lg text-center font-medium bg-white text-gray-900 shadow-sm text-sm">
                        消費結構
                    </button>
                    <button class="flex-1 py-2 rounded-lg text-center font-medium text-gray-500 text-sm">
                        收支趨勢
                    </button>
                </div>

                <!-- 消費結構圓餅圖 -->
                <div class="bg-white rounded-xl p-4 shadow-sm mb-6">
                    <h3 class="text-lg font-semibold mb-4">消費結構分析</h3>
                    <div class="flex items-center justify-center mb-6">
                        <div class="relative w-40 h-40">
                            <!-- 模擬圓餅圖 -->
                            <div class="w-40 h-40 rounded-full" style="background: conic-gradient(
                                #ef4444 0deg 144deg,
                                #3b82f6 144deg 216deg,
                                #8b5cf6 216deg 306deg,
                                #22c55e 306deg 360deg
                            );">
                                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-white rounded-full flex items-center justify-center">
                                    <div class="text-center">
                                        <p class="text-xs text-gray-500">總支出</p>
                                        <p class="text-sm font-bold">18,500</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 圖例 -->
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-red-500 rounded-full"></div>
                                <span class="text-sm">餐飲</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold">$7,400</p>
                                <p class="text-xs text-gray-500">40%</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                                <span class="text-sm">購物</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold">$4,625</p>
                                <p class="text-xs text-gray-500">25%</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                                <span class="text-sm">交通</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold">$3,700</p>
                                <p class="text-xs text-gray-500">20%</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                                <span class="text-sm">其他</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold">$2,775</p>
                                <p class="text-xs text-gray-500">15%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 每日支出趨勢 -->
                <div class="bg-white rounded-xl p-4 shadow-sm mb-6">
                    <h3 class="text-lg font-semibold mb-4">每日支出趨勢</h3>
                    <div class="h-32 flex items-end justify-between space-x-1">
                        <!-- 模擬長條圖 -->
                        <div class="flex flex-col items-center space-y-1">
                            <div class="w-6 bg-blue-500 rounded-t" style="height: 60px;"></div>
                            <span class="text-xs text-gray-500">1日</span>
                        </div>
                        <div class="flex flex-col items-center space-y-1">
                            <div class="w-6 bg-blue-500 rounded-t" style="height: 80px;"></div>
                            <span class="text-xs text-gray-500">2日</span>
                        </div>
                        <div class="flex flex-col items-center space-y-1">
                            <div class="w-6 bg-blue-500 rounded-t" style="height: 40px;"></div>
                            <span class="text-xs text-gray-500">3日</span>
                        </div>
                        <div class="flex flex-col items-center space-y-1">
                            <div class="w-6 bg-blue-500 rounded-t" style="height: 100px;"></div>
                            <span class="text-xs text-gray-500">4日</span>
                        </div>
                        <div class="flex flex-col items-center space-y-1">
                            <div class="w-6 bg-blue-500 rounded-t" style="height: 30px;"></div>
                            <span class="text-xs text-gray-500">5日</span>
                        </div>
                        <div class="flex flex-col items-center space-y-1">
                            <div class="w-6 bg-blue-500 rounded-t" style="height: 90px;"></div>
                            <span class="text-xs text-gray-500">6日</span>
                        </div>
                        <div class="flex flex-col items-center space-y-1">
                            <div class="w-6 bg-blue-500 rounded-t" style="height: 70px;"></div>
                            <span class="text-xs text-gray-500">7日</span>
                        </div>
                    </div>
                </div>

                <!-- 統計摘要 -->
                <div class="bg-white rounded-xl p-4 shadow-sm mb-6">
                    <h3 class="text-lg font-semibold mb-4">統計摘要</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-50 rounded-lg p-3">
                            <p class="text-xs text-gray-500 mb-1">平均每日支出</p>
                            <p class="text-lg font-bold text-gray-900">$597</p>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-3">
                            <p class="text-xs text-gray-500 mb-1">最高單日支出</p>
                            <p class="text-lg font-bold text-red-500">$1,280</p>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-3">
                            <p class="text-xs text-gray-500 mb-1">交易次數</p>
                            <p class="text-lg font-bold text-gray-900">31 筆</p>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-3">
                            <p class="text-xs text-gray-500 mb-1">最常消費類別</p>
                            <p class="text-lg font-bold text-blue-500">餐飲</p>
                        </div>
                    </div>
                </div>

                <!-- 比較分析 -->
                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <h3 class="text-lg font-semibold mb-4">與上月比較</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">總支出變化</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-semibold text-red-500">+12.5%</span>
                                <i class="fas fa-arrow-up text-red-500 text-xs"></i>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">平均每日支出</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-semibold text-green-500">-5.2%</span>
                                <i class="fas fa-arrow-down text-green-500 text-xs"></i>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">交易次數</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-semibold text-blue-500">+8 筆</span>
                                <i class="fas fa-arrow-up text-blue-500 text-xs"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Navigation -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200">
                <div class="grid grid-cols-5 py-2">
                    <button class="flex flex-col items-center py-2 text-gray-400">
                        <i class="fas fa-home text-xl mb-1"></i>
                        <span class="text-xs">主畫面</span>
                    </button>
                    <button class="flex flex-col items-center py-2 text-gray-400">
                        <i class="fas fa-list text-xl mb-1"></i>
                        <span class="text-xs">交易紀錄</span>
                    </button>
                    <button class="flex flex-col items-center py-2 text-blue-500">
                        <i class="fas fa-chart-bar text-xl mb-1"></i>
                        <span class="text-xs">報表分析</span>
                    </button>
                    <button class="flex flex-col items-center py-2 text-gray-400">
                        <i class="fas fa-tags text-xl mb-1"></i>
                        <span class="text-xs">類別管理</span>
                    </button>
                    <button class="flex flex-col items-center py-2 text-gray-400">
                        <i class="fas fa-cog text-xl mb-1"></i>
                        <span class="text-xs">設定</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 