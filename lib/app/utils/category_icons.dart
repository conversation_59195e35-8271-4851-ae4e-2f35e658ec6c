import 'package:flutter/material.dart';

class CategoryIcons {
  // 記帳分類圖標映射
  static final Map<String, IconData> iconMap = {
    // === 收入類別 ===
    'work': Icons.work, // 薪資
    'trending_up': Icons.trending_up, // 投資收益
    'business': Icons.business, // 兼職
    'card_giftcard': Icons.card_giftcard, // 獎金/紅利
    'account_balance': Icons.account_balance, // 銀行利息
    'sell': Icons.sell, // 出售物品
    'volunteer_activism': Icons.volunteer_activism, // 補助/津貼

    // === 飲食類別 ===
    'restaurant': Icons.restaurant, // 正餐
    'local_cafe': Icons.local_cafe, // 咖啡/茶飲
    'local_bar': Icons.local_bar, // 酒類
    'fastfood': Icons.fastfood, // 速食
    'lunch_dining': Icons.lunch_dining, // 午餐
    'dinner_dining': Icons.dinner_dining, // 晚餐
    'breakfast_dining': Icons.breakfast_dining, // 早餐
    'icecream': Icons.icecream, // 甜點/冰品
    'local_pizza': Icons.local_pizza, // 外賣

    // === 交通類別 ===
    'directions_car': Icons.directions_car, // 汽車
    'directions_bus': Icons.directions_bus, // 公車
    'directions_subway': Icons.directions_subway, // 地鐵
    'local_taxi': Icons.local_taxi, // 計程車
    'local_gas_station': Icons.local_gas_station, // 加油
    'two_wheeler': Icons.two_wheeler, // 機車
    'pedal_bike': Icons.pedal_bike, // 腳踏車
    'flight': Icons.flight, // 飛機
    'directions_boat': Icons.directions_boat, // 船
    'local_parking': Icons.local_parking, // 停車費

    // === 購物類別 ===
    'shopping_bag': Icons.shopping_bag, // 購物
    'checkroom': Icons.checkroom, // 服裝
    'phone_android': Icons.phone_android, // 電子產品
    'book': Icons.book, // 書籍
    'home_repair_service': Icons.home_repair_service, // 日用品
    'pets': Icons.pets, // 寵物用品
    'local_grocery_store': Icons.local_grocery_store, // 生鮮食品
    'store': Icons.store, // 超市

    // === 娛樂類別 ===
    'movie': Icons.movie, // 電影
    'sports_esports': Icons.sports_esports, // 遊戲
    'travel_explore': Icons.travel_explore, // 旅遊
    'sports_soccer': Icons.sports_soccer, // 運動
    'music_note': Icons.music_note, // 音樂/演唱會
    'local_activity': Icons.local_activity, // 娛樂活動
    'casino': Icons.casino, // 博弈

    // === 健康醫療 ===
    'local_hospital': Icons.local_hospital, // 醫療
    'local_pharmacy': Icons.local_pharmacy, // 藥品
    'fitness_center': Icons.fitness_center, // 健身
    'spa': Icons.spa, // 保養/SPA
    'security': Icons.security, // 保險
    'healing': Icons.healing, // 看病

    // === 居住類別 ===
    'home': Icons.home, // 房租/房貸
    'electrical_services': Icons.electrical_services, // 電費
    'water_drop': Icons.water_drop, // 水費
    'wifi': Icons.wifi, // 網路費
    'local_fire_department': Icons.local_fire_department, // 瓦斯費
    'cleaning_services': Icons.cleaning_services, // 清潔費
    'build': Icons.build, // 維修費
    'security_update_good': Icons.security_update_good, // 管理費

    // === 教育類別 ===
    'school': Icons.school, // 學費
    'library_books': Icons.library_books, // 教材
    'computer': Icons.computer, // 線上課程
    'assignment': Icons.assignment, // 考試/證照費
    'psychology': Icons.psychology, // 補習/培訓

    // === 金融相關 ===
    'attach_money': Icons.attach_money, // 現金
    'credit_card': Icons.credit_card, // 信用卡
    'savings': Icons.savings, // 儲蓄
    'monetization_on': Icons.monetization_on, // 投資
    'currency_exchange': Icons.currency_exchange, // 匯款/轉帳
    'payment': Icons.payment, // 貸款

    // === 其他類別 ===
    'redeem': Icons.redeem, // 禮品
    'favorite': Icons.favorite, // 捐贈/慈善
    'celebration': Icons.celebration, // 節日/慶祝
    'face': Icons.face, // 個人護理
    'phone': Icons.phone, // 通訊費
    'subscriptions': Icons.subscriptions, // 訂閱服務
    'receipt': Icons.receipt, // 稅費
    'miscellaneous_services': Icons.miscellaneous_services, // 其他服務

    // === 通用圖標 ===
    'category': Icons.category,
    'add': Icons.add,
    'remove': Icons.remove,
    'edit': Icons.edit,
    'delete': Icons.delete,
    'settings': Icons.settings,
    'money_off': Icons.money_off, // 支出
  };

  // 舊版鍵（以 IconData.toString() 為鍵）的兼容映射 -> 穩定字串鍵
  static final Map<String, String> legacyKeyMap = {
    // === 收入類別 ===
    '${Icons.work}': 'work',
    '${Icons.trending_up}': 'trending_up',
    '${Icons.business}': 'business',
    '${Icons.card_giftcard}': 'card_giftcard',
    '${Icons.account_balance}': 'account_balance',
    '${Icons.sell}': 'sell',
    '${Icons.volunteer_activism}': 'volunteer_activism',

    // === 飲食類別 ===
    '${Icons.restaurant}': 'restaurant',
    '${Icons.local_cafe}': 'local_cafe',
    '${Icons.local_bar}': 'local_bar',
    '${Icons.fastfood}': 'fastfood',
    '${Icons.lunch_dining}': 'lunch_dining',
    '${Icons.dinner_dining}': 'dinner_dining',
    '${Icons.breakfast_dining}': 'breakfast_dining',
    '${Icons.icecream}': 'icecream',
    '${Icons.local_pizza}': 'local_pizza',

    // === 交通類別 ===
    '${Icons.directions_car}': 'directions_car',
    '${Icons.directions_bus}': 'directions_bus',
    '${Icons.directions_subway}': 'directions_subway',
    '${Icons.local_taxi}': 'local_taxi',
    '${Icons.local_gas_station}': 'local_gas_station',
    '${Icons.two_wheeler}': 'two_wheeler',
    '${Icons.pedal_bike}': 'pedal_bike',
    '${Icons.flight}': 'flight',
    '${Icons.directions_boat}': 'directions_boat',
    '${Icons.local_parking}': 'local_parking',

    // === 購物類別 ===
    '${Icons.shopping_bag}': 'shopping_bag',
    '${Icons.checkroom}': 'checkroom',
    '${Icons.phone_android}': 'phone_android',
    '${Icons.book}': 'book',
    '${Icons.home_repair_service}': 'home_repair_service',
    '${Icons.pets}': 'pets',
    '${Icons.local_grocery_store}': 'local_grocery_store',
    '${Icons.store}': 'store',

    // === 娛樂類別 ===
    '${Icons.movie}': 'movie',
    '${Icons.sports_esports}': 'sports_esports',
    '${Icons.travel_explore}': 'travel_explore',
    '${Icons.sports_soccer}': 'sports_soccer',
    '${Icons.music_note}': 'music_note',
    '${Icons.local_activity}': 'local_activity',
    '${Icons.casino}': 'casino',

    // === 健康醫療 ===
    '${Icons.local_hospital}': 'local_hospital',
    '${Icons.local_pharmacy}': 'local_pharmacy',
    '${Icons.fitness_center}': 'fitness_center',
    '${Icons.spa}': 'spa',
    '${Icons.security}': 'security',
    '${Icons.healing}': 'healing',

    // === 居住類別 ===
    '${Icons.home}': 'home',
    '${Icons.electrical_services}': 'electrical_services',
    '${Icons.water_drop}': 'water_drop',
    '${Icons.wifi}': 'wifi',
    '${Icons.local_fire_department}': 'local_fire_department',
    '${Icons.cleaning_services}': 'cleaning_services',
    '${Icons.build}': 'build',
    '${Icons.security_update_good}': 'security_update_good',

    // === 教育類別 ===
    '${Icons.school}': 'school',
    '${Icons.library_books}': 'library_books',
    '${Icons.computer}': 'computer',
    '${Icons.assignment}': 'assignment',
    '${Icons.psychology}': 'psychology',

    // === 金融相關 ===
    '${Icons.attach_money}': 'attach_money',
    '${Icons.credit_card}': 'credit_card',
    '${Icons.savings}': 'savings',
    '${Icons.monetization_on}': 'monetization_on',
    '${Icons.currency_exchange}': 'currency_exchange',
    '${Icons.payment}': 'payment',

    // === 其他類別 ===
    '${Icons.redeem}': 'redeem',
    '${Icons.favorite}': 'favorite',
    '${Icons.celebration}': 'celebration',
    '${Icons.face}': 'face',
    '${Icons.phone}': 'phone',
    '${Icons.subscriptions}': 'subscriptions',
    '${Icons.receipt}': 'receipt',
    '${Icons.miscellaneous_services}': 'miscellaneous_services',

    // === 通用圖標 ===
    '${Icons.category}': 'category',
    '${Icons.add}': 'add',
    '${Icons.remove}': 'remove',
    '${Icons.edit}': 'edit',
    '${Icons.delete}': 'delete',
    '${Icons.settings}': 'settings',
    '${Icons.money_off}': 'money_off',
  };

  /// 根据分类名称获取图标
  static IconData getIcon(String? iconName) {
    // 空值處理
    if (iconName == null || iconName.isEmpty) {
      return availableIcons.first;
    }
    // 先以新鍵查找
    final direct = iconMap[iconName];
    if (direct != null) return direct;
    // 再嘗試舊鍵映射
    final mappedKey = legacyKeyMap[iconName];
    if (mappedKey != null) {
      final legacy = iconMap[mappedKey];
      if (legacy != null) return legacy;
    }
    // 兜底
    return availableIcons.first;
  }

  static String getIconName(IconData icon) {
    return iconMap.entries
        .firstWhere(
          (entry) => entry.value == icon,
          orElse: () => const MapEntry('category', Icons.category),
        )
        .key;
  }

  static Iterable<IconData> get availableIcons => iconMap.values;

  /// 獲取所有可用圖標名稱
  static Iterable<String> get getAllIconNames => iconMap.keys;

  /// 檢查圖標是否存在
  static bool hasIcon(String? iconName) {
    if (iconName == null || iconName.isEmpty) {
      return false;
    }
    return iconMap.containsKey(iconName) || legacyKeyMap.containsKey(iconName);
  }
}
