import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/constants.dart';
import 'package:pocket_trac/extension.dart';

import '../../../../colors.dart';
import '../controllers/location_picker_controller.dart';

class LocationPickerView extends GetView<LocationPickerController> {
  const LocationPickerView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? ErpColors.darkBackground : ErpColors.background,
      appBar: AppBar(
        title: const Text('選擇地點'),
        backgroundColor: isDark ? ErpColors.darkCardBackground : Colors.white,
        foregroundColor:
            isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        elevation: 0,
      ),
      body: controller.obx(
        (state) => Stack(
          children: _buildBodyChildren(context).toList(growable: false),
        ),
        onLoading: const Center(
          child: CircularProgressIndicator(),
        ),
        onError: (error) => Center(
          child: Text(error.toString()),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: controller.confirmLocation,
        child: const Icon(Icons.save),
      ),
    );
  }

  Iterable<Widget> _buildBodyChildren(BuildContext context) sync* {
    // 地圖主體
    yield _buildMap(context);

    // 地點資訊卡片
    yield Positioned(
      top: 16,
      left: 16,
      right: 16,
      child: Obx(() => _buildLocationInfoCard(context)),
    );

    // 地圖控制按鈕
    yield Positioned(
      right: 16,
      bottom: context.fabBottomPadding(includeSafeArea: true) + 40, // 依 FAB 與安全區域動態避讓，並保留額外間距
      child: _buildMapControls(context),
    );
  }

  // 建立地圖
  Widget _buildMap(BuildContext context) {
    Iterable<Widget> getChildren() sync* {
      // 地圖主體
      yield Obx(() {
        return FlutterMap(
          mapController: controller.mapController,
          options: MapOptions(
            initialCenter: controller.selectedLocation,
            initialZoom: controller.currentZoom,
            onPositionChanged: (camera, hasGesture) {
              if (hasGesture) {
                controller.onMapPositionChanged(camera);
              }
            },
            interactionOptions: const InteractionOptions(
              flags: InteractiveFlag.all,
            ),
          ),
          children: _mapLayers().toList(growable: false),
        );
      });

      // 中心標記 - 固定在地圖中心
      yield Center(
        child: Container(
          width: 40,
          height: 40,
          decoration: const BoxDecoration(
            color: Colors.transparent,
          ),
          child: const Icon(
            Icons.location_on,
            color: Colors.red,
            size: 40,
          ),
        ),
      );
    }

    return Stack(
      children: getChildren().toList(growable: false),
    );
  }

  Iterable<Widget> _mapLayers() sync* {
    // 地圖圖層 - 使用台灣國土測繪中心的 WMTS 服務
    yield TileLayer(
      urlTemplate:
          'https://wmts.nlsc.gov.tw/wmts/EMAP/default/GoogleMapsCompatible/{z}/{y}/{x}',
      userAgentPackageName: 'com.example.pocket_trac',
      maxZoom: Constants.maxZoomLevel,
      minZoom: Constants.minZoomLevel,
    );
  }

  // 建立地點資訊卡片
  Widget _buildLocationInfoCard(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Row(
        children: _infoHeaderRowChildren(isDark).toList(growable: false),
      );
      yield const SizedBox(height: 8);
      yield Text(
        '緯度: ${controller.selectedLocation.latitude.toStringAsFixed(6)}',
        style: TextStyle(
          fontSize: 14,
          color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
        ),
      );
      yield Text(
        '經度: ${controller.selectedLocation.longitude.toStringAsFixed(6)}',
        style: TextStyle(
          fontSize: 14,
          color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ErpColors.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: getChildren().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _infoHeaderRowChildren(bool isDark) sync* {
    yield const Icon(
      Icons.location_on,
      color: ErpColors.primary,
      size: 20,
    );
    yield const SizedBox(width: 8);
    yield Text(
      '選定位置',
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
      ),
    );
  }

  // 建立地圖控制按鈕
  Widget _buildMapControls(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      // 放大按鈕
      yield DecoratedBox(
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          onPressed: controller.zoomIn,
          icon: const Icon(Icons.add),
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      // 縮小按鈕
      yield DecoratedBox(
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          onPressed: controller.zoomOut,
          icon: const Icon(Icons.remove),
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      // 定位按鈕
      yield DecoratedBox(
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          onPressed: controller.moveToMyLocation,
          icon: const Icon(Icons.my_location),
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
    }

    return Column(
      children: getChildren().toList(growable: false),
    );
  }
}
