import 'package:get/get.dart';
import 'package:pocket_trac/app/extensions/box_provider_extension.dart';
import 'package:pocket_trac/app/providers/box_provider.dart';
import 'package:pocket_trac/app/providers/pref_provider.dart';
import 'package:pocket_trac/app/routes/app_pages.dart';
import 'package:talker_flutter/talker_flutter.dart';

class SplashController extends GetxController with StateMixin<String> {
  final PrefProvider prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Talker get talker => boxProvider.talker;

  SplashController({
    required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    change('', status: RxStatus.loading());
    try {
      await boxProvider.initBoxes();
      Get.offNamed(Routes.INDEX);
    } catch (e, s) {
      talker.error('$e', e, s);
      change(null, status: RxStatus.error('載入分類失敗: $e'));
    }
  }
}
