import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:omni_datetime_picker/omni_datetime_picker.dart';
import 'package:pocket_trac/app/widgets/category_grid_view.dart';
import 'package:pocket_trac/app/widgets/location_view.dart';
import 'package:pocket_trac/app/widgets/number_keypad.dart';
import 'package:pocket_trac/app/widgets/sheet_wrapper.dart';
import 'package:pocket_trac/app/widgets/transaction_type_selector.dart';
import 'package:pocket_trac/app/modules/category_detail/bindings/category_detail_binding.dart';
import 'package:pocket_trac/app/modules/category_detail/views/category_detail_view.dart';
import 'package:pocket_trac/constants.dart';
import 'package:pocket_trac/keys.dart';

import '../../../../colors.dart';
import '../../../../extension.dart';
import '../../../models/erp_category.dart';
import '../../../routes/app_pages.dart';
import '../controllers/order_detail_controller.dart';

class OrderDetailView extends GetView<OrderDetailController> {
  OrderDetailView({super.key});

  // 表單 key 僅屬於 View 的 UI 狀態
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    // 為避免內容被 FloatingActionButton 遮擋，統一使用擴展方法計算底部內距
    final bottomListPadding = context.fabBottomPadding(
      contentPadding: 16.0,
      fabHeight: 56.0,
      includeSafeArea: false, // 已使用 SafeArea 包裹 body
    );

    return Scaffold(
      backgroundColor: isDark ? ErpColors.darkBackground : ErpColors.background,
      appBar: AppBar(
        title: const Text('交易詳情'),
        elevation: 0,
      ),
      body: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          FocusScope.of(context).unfocus();
          controller.hideNoteSuggestions();
        },
        child: SafeArea(
          child: controller.obx((state) {
            return Form(
              key: _formKey,
              child: ListView(
                padding: EdgeInsets.fromLTRB(16, 16, 16, bottomListPadding),
                children: _buildBody(context).toList(growable: false),
              ),
            );
          }),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          final form = _formKey.currentState;
          if (form == null || form.validate()) {
            controller.saveTransaction();
          }
        },
        backgroundColor: ErpColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.save),
      ),
    );
  }

  Iterable<Widget> _buildBody(BuildContext context) sync* {
    yield _buildAmountSection(context);
    yield const SizedBox(height: 8);
    yield NumberKeypad(
      onDigitPressed: controller.addDigit,
      onDeletePressed: controller.deleteDigit,
    );
    yield const SizedBox(height: 16);
    yield _buildNoteInput(context);
    yield const SizedBox(height: 16);
    yield _buildCategoryAndDateTimeRow(context);
    yield const SizedBox(height: 16);
    yield _buildTransactionTypeSelector(context);
    yield const SizedBox(height: 16);
    yield _buildLocationSelector(context);
  }

  // 交易類型選擇器
  Widget _buildTransactionTypeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '交易類型',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield Obx(() {
        return TransactionTypeSelector(
          transactionType: controller.draft.transactionType,
          onChanged: (value) {
            controller.draft.transactionType = value;
            controller.refreshDraft();
          },
        );
      });
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  Iterable<Widget> _amountRowChildren(bool isDark) sync* {
    // 移除貨幣符號
    yield Expanded(
      child: Obx(() => Text(
            controller.amountText,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
            ),
          )),
    );
  }

  // 金額輸入區域
  Widget _buildAmountSection(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '金額',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          border: Border.all(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: _amountRowChildren(isDark).toList(growable: false),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  // 分類和日期時間選擇器行
  Widget _buildCategoryAndDateTimeRow(BuildContext context) {
    return Row(
      children: _categoryDateTimeRowChildren(context).toList(growable: false),
    );
  }

  Iterable<Widget> _categoryDateTimeRowChildren(BuildContext context) sync* {
    yield Expanded(child: _buildCategorySelector(context));
    yield const SizedBox(width: 12);
    yield Expanded(child: _buildDateTimeSelector(context));
  }

  // 分類選擇器
  Widget _buildCategorySelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return FormField<ErpCategory?>(
      validator: (value) {
        return controller.draft.parent.target == null ? '請選擇類別' : null;
      },
      builder: (state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: _categorySelectorChildren(context, isDark, state)
              .toList(growable: false),
        );
      },
    );
  }

  Iterable<Widget> _categorySelectorChildren(BuildContext context, bool isDark,
      FormFieldState<ErpCategory?> state) sync* {
    yield Text(
      '消費類別',
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
      ),
    );
    yield const SizedBox(height: 8);
    yield Obx(() => GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
            controller.hideNoteSuggestions();
            _showCategoryPicker(context, fieldState: state);
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isDark ? ErpColors.darkCardBackground : Colors.white,
              border: Border.all(
                color: state.hasError
                    ? Theme.of(context).colorScheme.error
                    : (isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB)),
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: _categoryValueRowChildren(context, isDark)
                  .toList(growable: false),
            ),
          ),
        ));
    if (state.hasError) {
      yield const SizedBox(height: 6);
      yield Text(
        state.errorText ?? '',
        style: TextStyle(
          fontSize: 12,
          color: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Iterable<Widget> _categoryValueRowChildren(
      BuildContext context, bool isDark) sync* {
    if (controller.draft.parent.target != null) {
      yield Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color:
              controller.draft.parent.target!.getColor().withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(
          controller.draft.parent.target!.getIcon(),
          size: 12,
          color: controller.draft.parent.target!.getColor(),
        ),
      );
      yield const SizedBox(width: 8);
      yield Expanded(
        child: Text(
          controller.draft.parent.target!.name ?? '',
          style: TextStyle(
            fontSize: 14,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      );
    } else {
      yield Expanded(
        child: Text(
          '請選擇類別',
          style: TextStyle(
            fontSize: 14,
            color:
                isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
          ),
        ),
      );
    }
    yield Icon(
      Icons.keyboard_arrow_down,
      color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
      size: 16,
    );
  }

  // 日期時間選擇器
  Widget _buildDateTimeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '日期時間',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
          controller.hideNoteSuggestions();
          _showDateTimePicker(context);
        },
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            border: Border.all(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Obx(() {
            return Text(
              DateFormat('MM-dd HH:mm')
                  .format(controller.draft.triggerAt ?? DateTime.now()),
              style: TextStyle(
                fontSize: 14,
                color:
                    isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
              ),
            );
          }),
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  // 備註輸入
  Widget _buildNoteInput(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _noteInputChildren(context, isDark).toList(growable: false),
    );
  }

  Iterable<Widget> _noteInputChildren(BuildContext context, bool isDark) sync* {
    yield Text(
      '備註',
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
      ),
    );
    yield const SizedBox(height: 8);
    yield CompositedTransformTarget(
      link: controller.layerLink,
      child: Container(
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          border: Border.all(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: TextFormField(
          focusNode: controller.noteFocusNode,
          controller: controller.noteTextController,
          onChanged: controller.setNote,
          onFieldSubmitted: (_) {
            controller.hideNoteSuggestions();
            FocusScope.of(context).unfocus();
          },
          onTap: () {
            // 當點擊輸入框時，如果有內容則顯示建議
            final text = controller.noteTextController.text;
            if (text.trim().isNotEmpty) {
              controller.searchNoteSuggestions(text);
            }
          },
          maxLines: 1,
          decoration: InputDecoration(
            hintText: '添加備註...',
            hintStyle: TextStyle(
              color: isDark
                  ? ErpColors.darkTextSecondary
                  : ErpColors.textSecondary,
            ),
            border: InputBorder.none,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          style: TextStyle(
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
      ),
    );
    // 自動完成建議列表 - 使用 Overlay
    yield Obx(() {
      if (controller.showNoteSuggestions.value &&
          controller.noteSuggestions.isNotEmpty) {
        // 確保 overlay 在下一幀顯示
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.showOverlay(context);
        });
      } else {
        controller.hideOverlay();
      }
      return const SizedBox.shrink();
    });
  }

  // 地點選擇器
  Widget _buildLocationSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '地點',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield Obx(() {
        final lat = controller.draft.latitude ?? 0.0;
        final lng = controller.draft.longitude ?? 0.0;
        return LocationView(
          key: ValueKey('$lat,$lng'),
          location: controller.draft.latLng,
          onTap: () {
            FocusScope.of(context).unfocus();
            controller.hideNoteSuggestions();
            _showLocationPicker(context);
          },
        );
      });
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  // 顯示日期時間選擇器
  Future<void> _showDateTimePicker(BuildContext context) async {
    final DateTime? pickedDateTime = await showOmniDateTimePicker(
      context: context,
      initialDate: controller.draft.triggerAt,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      is24HourMode: true,
      isShowSeconds: false,
      minutesInterval: 1,
      secondsInterval: 1,
      borderRadius: const BorderRadius.all(Radius.circular(16)),
      constraints: const BoxConstraints(
        maxWidth: 350,
        maxHeight: 650,
      ),
      transitionBuilder: (context, anim1, anim2, child) {
        return FadeTransition(
          opacity: anim1.drive(
            Tween(
              begin: 0,
              end: 1,
            ),
          ),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      barrierDismissible: true,
      selectableDayPredicate: (dateTime) {
        // 可以在這裡添加日期限制邏輯
        return true;
      },
    );

    if (pickedDateTime != null) {
      controller.draft.triggerAt = pickedDateTime;
      controller.refreshDraft();
    }
  }

  // 顯示分類選擇器彈窗
  Future<void> _showCategoryPicker(BuildContext context,
      {FormFieldState<ErpCategory?>? fieldState}) async {
    await SheetWrapper(
      showHeader: true,
      title: '選擇類別',
      child: Obx(() {
        return CategoryGridView(
          categories: controller.categories,
          selectedCategory: controller.draft.parent.target,
          onCategorySelected: (category) {
            controller.setCategory(category);
            fieldState?.didChange(category);
            Navigator.of(context).pop();
          },
          showAddButton: true,
          onAddCategory: () => _showAddCategorySheet(context),
        );
      }),
    ).sheet(
      isDismissible: true,
      enableDrag: true,
      isScrollControlled: true,
      ignoreSafeArea: true,
    );
  }

  // 顯示新增/編輯分類的彈窗，保存後刷新並選中新分類
  Future<void> _showAddCategorySheet(BuildContext context) async {
    final result = await SheetWrapper(
      title: '新增分類',
      onInit: () {
        // 初始化分類詳細頁綁定，並設置參數 id=0 代表新增
        Get.parameters['id'] = '0';
        final binding = CategoryDetailBinding();
        binding.dependencies();
      },
      child: const CategoryDetailView(),
    ).sheet(
      isDismissible: true,
      enableDrag: true,
      isScrollControlled: true,
      ignoreSafeArea: true,
    );

    // 如果保存成功，CategoryDetailController 會透過 Get.back(result: <int id>) 返回 id
    if (result is int && result > 0) {
      try {
        // 重新載入分類列表
        final list = await controller.categoryRepository.getAllAsync();
        controller.categories.assignAll(list);

        // 取回新建分類並設為選中
        final created =
            await controller.categoryRepository.getByIdAsync(result);
        if (created != null) {
          controller.setCategory(created);
          controller.refreshDraft();
        }
      } catch (e) {
        // 失敗不致命，保持原狀
      }
    }
  }

  // 顯示地點選擇器
  Future<void> _showLocationPicker(BuildContext context) async {
    final draft = controller.draft;
    final result = await Get.toNamed(Routes.LOCATION_PICKER, parameters: {
      Keys.latitude: '${draft.latitude ?? Constants.taipeiLatitude}',
      Keys.longitude: '${draft.longitude ?? Constants.taipeiLongitude}',
    });

    if (result != null && result is Map<String, dynamic>) {
      final latitude = result[Keys.latitude] as double?;
      final longitude = result[Keys.longitude] as double?;

      controller.draft.latitude = latitude;
      controller.draft.longitude = longitude;
      controller.refreshDraft();
    }
  }
}
