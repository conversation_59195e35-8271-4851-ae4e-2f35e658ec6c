import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:pocket_trac/constants.dart';
import 'package:pocket_trac/enums.dart';
import 'package:pocket_trac/extension.dart';
import 'package:pocket_trac/keys.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:pocket_trac/app/providers/pref_provider.dart';

import '../../../../colors.dart';
import '../../../models/erp_category.dart';
import '../../../models/erp_order.dart';
import '../../../repositories/category_repository.dart';
import '../../../repositories/order_repository.dart';

class OrderDetailController extends GetxController with StateMixin<String> {
  final CategoryRepository categoryRepository;
  final OrderRepository orderRepository;
  final PrefProvider prefProvider;
  Talker get talker => orderRepository.talker;

  final categories = <ErpCategory>[].obs;

  // 金額輸入緩衝字串，確保顯示能保留使用者輸入格式（例如 0. 或 12.）
  final _amountBuffer = '0'.obs;
  String get amountText => _amountBuffer.value;

  // 備註自動完成相關
  final noteSuggestions = <String>[].obs;
  final showNoteSuggestions = false.obs;
  final layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  // 備註輸入控制器，確保選擇建議時可以即時更新輸入框內容
  final TextEditingController noteTextController = TextEditingController();
  // 備註輸入焦點管理：失焦時自動隱藏建議
  final FocusNode noteFocusNode = FocusNode();

  // 儲存按鈕狀態管理
  final saveButtonState = ''.reactive;

  final _draft = ErpOrder(type: TransactionType.expense.index).obs;
  ErpOrder get draft => _draft.value;

  void refreshDraft() {
    _draft.refresh();
  }

  OrderDetailController({
    required this.categoryRepository,
    required this.orderRepository,
    required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
    saveButtonState.change('', status: RxStatus.success());
    // 監聽備註輸入框焦點變化：失焦時關閉建議
    noteFocusNode.addListener(() {
      if (!noteFocusNode.hasFocus) {
        hideNoteSuggestions();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    hideOverlay(); // 清理 overlay
    noteTextController.dispose();
    noteFocusNode.dispose();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.loading());
      await _loadOrder();
      await _loadCategories();
      change('', status: RxStatus.success());
    } catch (e, s) {
      talker.error('載入分類失敗: $e', e, s);
      change(null, status: RxStatus.error('載入分類失敗: $e'));
    }
  }

  Future<LatLng> _getLatestLocation() async {
    try {
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (serviceEnabled) {
        LocationPermission permission = await Geolocator.checkPermission();
        if (permission != LocationPermission.denied &&
            permission != LocationPermission.deniedForever) {
          final pos = await Geolocator.getLastKnownPosition();
          if (pos != null) {
            return LatLng(pos.latitude, pos.longitude);
          }
        }
      }
    } catch (_) {
      // 取得系統最後位置失敗時忽略
    }

    // prefProvider 未註冊則使用預設位置
    final last = prefProvider.lastLocation;
    if (last != null) {
      return LatLng(last.latitude, last.longitude);
    }

    // 取得系統最後位置失敗時使用預設位置
    return const LatLng(Constants.taipeiLatitude, Constants.taipeiLongitude);
  }

  Future<void> _loadOrder() async {
    final id = int.tryParse(Get.parameters[Keys.id] ?? '') ?? 0;
    try {
      // id == 0 代表建立新訂單，不需查詢資料庫
      final order = id == 0 ? null : await orderRepository.getByIdAsync(id);
      // 編輯既有訂單
      if (order != null) {
        _draft.value = order;
      }
      // 新增狀態
      if (order == null) {
        final last = await _getLatestLocation();
        _draft.value.latitude = last.latitude;
        _draft.value.longitude = last.longitude;
      }
      // 以現有金額初始化輸入緩衝
      _amountBuffer.value = _draft.value.amountText;
      // 初始化備註輸入框
      noteTextController.text = _draft.value.note ?? '';
    } catch (e, s) {
      talker.error('載入訂單失敗: $e', e, s);
    }
  }

  // 載入分類列表
  Future<void> _loadCategories() async {
    try {
      final categoryList = await categoryRepository.getAllAsync();
      categories.value = categoryList;

      // 載入完分類後，設置最新交易的類別作為默認值
      await _setDefaultCategoryFromLatestOrder();
    } catch (e) {
      // 僅記錄錯誤，不在此顯示 Snackbar；交由畫面層決定是否提示
      talker.error('載入分類失敗: $e');
    }
  }

  // 從最新交易設置默認類別
  Future<void> _setDefaultCategoryFromLatestOrder() async {
    try {
      // 若草稿已經有分類（例如編輯既有交易），則不覆寫
      if (draft.parent.target != null) return;

      final latestOrder = await orderRepository.getLatestOrderAsync();
      ErpCategory? categoryToUse;

      if (latestOrder != null && latestOrder.parent.target != null) {
        // 檢查最新交易的類別是否在當前分類列表中
        final latestCategory = latestOrder.parent.target!;
        final categoryExists =
            categories.any((cat) => cat.id == latestCategory.id);

        if (categoryExists) {
          categoryToUse = latestCategory;
        }
      }

      // 如果沒有最新交易、沒有類別、或不在列表中，則使用第一個分類作為回退
      categoryToUse ??= categories.isNotEmpty ? categories.first : null;

      if (categoryToUse != null) {
        draft.parent.target = categoryToUse;
        refreshDraft();
      }
    } catch (e) {
      // 如果獲取最新交易失敗，不影響正常流程，只記錄錯誤
      // 這裡不顯示錯誤給用戶，因為這只是一個便利功能
    }
  }

  // 數字鍵盤輸入
  void addDigit(String digit) {
    var currentValue = _amountBuffer.value;

    // 小數點最多一次
    if (digit == '.' && currentValue.contains('.')) {
      return;
    }

    // 0 的處理
    if (currentValue == '0') {
      if (digit == '.') {
        // 顯示為 0.（可視化保留小數點）
        _amountBuffer.value = '0.';
      } else {
        _amountBuffer.value = digit;
      }
    } else {
      _amountBuffer.value = currentValue + digit;
    }

    // 解析時若結尾為 .，以 .0 解析，但不改變顯示緩衝
    final parseTarget = _amountBuffer.value.endsWith('.')
        ? '${_amountBuffer.value}0'
        : _amountBuffer.value;
    final parsed = double.tryParse(parseTarget);
    if (parsed != null) {
      draft.amount = parsed;
      refreshDraft();
    }
  }

  // 刪除數字
  void deleteDigit() {
    var currentValue = _amountBuffer.value;

    if (currentValue.isNotEmpty) {
      var newValue = currentValue.substring(0, currentValue.length - 1);
      if (newValue.isEmpty) newValue = '0';

      _amountBuffer.value = newValue;

      // 解析：若結尾為 .，以 .0 解析
      final parseTarget = newValue.endsWith('.') ? '${newValue}0' : newValue;
      draft.amount = double.tryParse(parseTarget) ?? 0.0;
      refreshDraft();
    }
  }

  // 設置分類
  void setCategory(ErpCategory? category) {
    draft.parent.target = category;
    refreshDraft();
  }

  // 設置備註
  void setNote(String noteText) {
    draft.note = noteText.isEmpty ? null : noteText;
    refreshDraft();

    // 觸發自動完成搜尋
    if (noteText.trim().isNotEmpty) {
      _searchNoteSuggestions(noteText);
    } else {
      showNoteSuggestions.value = false;
      noteSuggestions.clear();
    }
  }

  // 搜尋備註建議
  Future<void> _searchNoteSuggestions(String query) async {
    try {
      if (query.trim().isEmpty) {
        showNoteSuggestions.value = false;
        noteSuggestions.clear();
        return;
      }

      final suggestions =
          await orderRepository.searchNotesAsync(query, limit: 10);

      // 過濾掉與當前輸入完全相同的建議
      final filteredSuggestions = suggestions
          .where(
              (suggestion) => suggestion.toLowerCase() != query.toLowerCase())
          .toList();

      noteSuggestions.value = filteredSuggestions;
      showNoteSuggestions.value = filteredSuggestions.isNotEmpty;
    } catch (e) {
      // 如果搜尋失敗，不顯示建議
      showNoteSuggestions.value = false;
      noteSuggestions.clear();
    }
  }

  // 選擇備註建議
  void selectNoteSuggestion(String suggestion) {
    draft.note = suggestion.isEmpty ? null : suggestion;
    refreshDraft();
    // 同步到輸入框並將游標移到文字末尾
    noteTextController.value = TextEditingValue(
      text: suggestion,
      selection: TextSelection.collapsed(offset: suggestion.length),
    );
    showNoteSuggestions.value = false;
    noteSuggestions.clear();
  }

  // 隱藏備註建議
  void hideNoteSuggestions() {
    showNoteSuggestions.value = false;
  }

  // 公開的搜尋備註建議方法
  Future<void> searchNoteSuggestions(String query) async {
    await _searchNoteSuggestions(query);
  }

  // 顯示 Overlay
  void showOverlay(BuildContext context) {
    if (_overlayEntry != null) return;

    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: MediaQuery.of(context).size.width - 32, // 考慮左右邊距
        child: CompositedTransformFollower(
          link: layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 48), // 位於輸入框下方
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(12),
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: isDark ? ErpColors.darkCardBackground : Colors.white,
                border: Border.all(
                  color:
                      isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                itemCount: noteSuggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = noteSuggestions[index];
                  return InkWell(
                    onTap: () => selectNoteSuggestion(suggestion),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.history,
                            size: 16,
                            color: isDark
                                ? ErpColors.darkTextSecondary
                                : ErpColors.textSecondary,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              suggestion,
                              style: TextStyle(
                                fontSize: 14,
                                color: isDark
                                    ? ErpColors.darkTextPrimary
                                    : ErpColors.textPrimary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  // 隱藏 Overlay
  void hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  // 儲存交易
  Future<void> saveTransaction() async {
    try {
      if (draft.parent.target == null) {
        return;
      }

      // 設置按鈕為載入狀態
      saveButtonState.change('', status: RxStatus.loading());

      // 直接使用 draft 物件，不另建 ErpOrder
      // 正規化欄位
      if ((draft.note ?? '').isEmpty) {
        draft.note = null;
      }
      // 若未設定觸發時間，預設為現在時間
      draft.triggerAt ??= DateTime.now();
      // 若未設定交易類型，預設支出
      draft.type ??= TransactionType.expense.index;
      // 儲存到資料庫（自動處理新增或更新以及時間戳）
      await orderRepository.saveAsync(draft);

      // 設置按鈕為成功狀態
      saveButtonState.change('', status: RxStatus.success());
      // 導航返回並告知列表頁操作結果
      Get.back(result: {
        'action': 'save',
        'ok': true,
      });
    } catch (e) {
      // 設置按鈕為錯誤狀態
      saveButtonState.change(null, status: RxStatus.error('儲存失敗: $e'));
      // 返回錯誤結果，由列表頁顯示提示
      Get.back(result: {
        'action': 'save',
        'ok': false,
        'message': '儲存失敗: $e',
      });
    }
  }
}
