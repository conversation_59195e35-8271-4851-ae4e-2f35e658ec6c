import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/widgets/gradient_background.dart';
import 'package:pocket_trac/app/widgets/order_item.dart';
import 'package:pocket_trac/app/routes/app_pages.dart';
import 'package:pocket_trac/keys.dart';

import '../controllers/filter_orders_controller.dart';

class FilterOrdersView extends GetView<FilterOrdersController> {
  const FilterOrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    // Get categoryId from parameters to show in title
    final categoryIdParam = Get.parameters['categoryId'];
    final title = categoryIdParam != null
        ? 'Filtered Orders - Category $categoryIdParam'
        : 'Filtered Orders';

    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (controller.filteredOrders.isEmpty) {
            return _buildEmptyState(context);
          }

          return GradientBackground(
            child: _buildOrdersList(context),
          );
        }),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.filter_list_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No orders found for this category',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.filteredOrders.length,
      itemBuilder: (context, index) {
        final order = controller.filteredOrders[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: OrderItem(
            order: order,
            onTap: () {
              Get.toNamed(
                Routes.ORDER_DETAIL,
                parameters: {Keys.id: '${order.id ?? 0}'},
              );
            },
          ),
        );
      },
    );
  }
}
