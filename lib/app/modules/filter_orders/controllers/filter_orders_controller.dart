import 'package:get/get.dart';

import '../../../models/erp_order.dart';
import '../../../repositories/order_repository.dart';

class FilterOrdersController extends GetxController {
  final OrderRepository orderRepository = Get.find<OrderRepository>();

  // Observable list to store filtered orders
  final RxList<ErpOrder> filteredOrders = <ErpOrder>[].obs;

  // Loading state
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadFilteredOrders();
  }


  void _loadFilteredOrders() async {
    try {
      isLoading.value = true;

      // Get categoryId from Get.parameters
      final categoryIdParam = Get.parameters['categoryId'];
      int? categoryId;

      if (categoryIdParam != null && categoryIdParam.isNotEmpty) {
        categoryId = int.tryParse(categoryIdParam);
      }

      // Create filter with categoryId
      final filter = OrderFilter(
        categoryId: categoryId,
        includeDeleted: false,
      );

      // Fetch filtered orders
      final orders = await orderRepository.getOrdersAsync(filter);
      filteredOrders.assignAll(orders);
    } catch (e, s) {
      // Handle error - log the error using talker
      orderRepository.talker.error('Error loading filtered orders: $e', e, s);
      filteredOrders.clear();
    } finally {
      isLoading.value = false;
    }
  }

  // Helper method to format amount from cents to currency
  String formatAmount(int? amountCents) {
    if (amountCents == null) return '0';
    final amount = amountCents / 100.0;
    return amount.toStringAsFixed(2);
  }

  // Helper method to format date
  String formatDate(DateTime? date) {
    if (date == null) return '';
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
