import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/modules/analysis/controllers/analysis_controller.dart';
import 'package:pocket_trac/app/utils/category_icons.dart';
import 'package:pocket_trac/extension.dart';

class CategoryLegendItem extends StatelessWidget {
  final CategoryData category;
  final VoidCallback? onTap;

  const CategoryLegendItem({
    super.key,
    required this.category,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = _parseColor(category.color);
    final lightColor = color.withValues(alpha: 0.1);
    final iconData = CategoryIcons.getIcon(category.icon);

    return ListTile(
      minLeadingWidth: 40,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: context.cardOverlayBorder(),
        ),
      ),
      dense: true,
      visualDensity: VisualDensity.compact,
      tileColor: context.cardOverlay(),
      contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12),
      onTap: onTap,
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: lightColor,
          borderRadius: BorderRadius.circular(20),
        ),
        alignment: Alignment.center,
        child: Icon(iconData, color: color, size: 20),
      ),
      title: Text(
        category.name,
        style:
            theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        '\$${category.amount.formattedAmount}',
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Text(
        '${category.percentage.toStringAsFixed(0)}%',
        style: theme.textTheme.bodySmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  Color _parseColor(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    return Color(int.parse('FF$hexColor', radix: 16));
  }
}
