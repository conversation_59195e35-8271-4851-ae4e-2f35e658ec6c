import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:pocket_trac/app/routes/app_pages.dart';
import 'package:pocket_trac/app/widgets/card_overlay.dart';
import 'package:pocket_trac/app/widgets/gradient_background.dart';

import '../controllers/analysis_controller.dart';
import '../../../../colors.dart';
import 'package:pocket_trac/extension.dart';
import '../widgets/category_legend_item.dart';

class AnalysisView extends GetView<AnalysisController> {
  const AnalysisView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: CustomScrollView(
          slivers: _slivers(context).toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _slivers(BuildContext context) sync* {
    final theme = Theme.of(context);
    yield SliverLayoutBuilder(
      builder: (context, constraints) {
        const double expandedH = 284;
        final bool isCollapsed =
            constraints.scrollOffset > (expandedH - kToolbarHeight);
        return SliverAppBar(
          pinned: true,
          floating: false,
          snap: false,
          expandedHeight: expandedH,
          elevation: 0,
          scrolledUnderElevation: 0,
          shape: null,
          leading: IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.arrow_back),
          ),
          centerTitle: true,
          title: isCollapsed ? const Text('分析') : null,
          flexibleSpace: FlexibleSpaceBar(
            titlePadding: const EdgeInsetsDirectional.only(
                start: 56, bottom: 12, end: 16),
            collapseMode: CollapseMode.parallax,
            background: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: theme.brightness == Brightness.dark
                      ? [ErpColors.darkGradientStart, ErpColors.darkGradientEnd]
                      : [ErpColors.gradientStart, ErpColors.gradientEnd],
                ),
              ),
              child: SafeArea(
                bottom: false,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 56, 16, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: _flexibleHeaderItems().toList(growable: false),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
    // Section A: 節點一（選擇器 + 主圖（不含分類清單））
    yield SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      sliver: SliverList(
        delegate: SliverChildListDelegate(
          _sliverSectionAItems().toList(growable: false),
        ),
      ),
    );

    // Section B: 只有在「消費結構」時顯示 SliverList 的分類清單
    yield Obx(() {
      if (controller.isLoading) {
        return const SliverToBoxAdapter(
          child: Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(ErpColors.primary),
              ),
            ),
          ),
        );
      }
      if (controller.selectedChartType == ChartType.structure) {
        return SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: _buildCategoryLegend(),
        );
      }
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    });

    // Section C: 其他統計區塊
    yield SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverList(
        delegate: SliverChildListDelegate(
          _sliverSectionCItems().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _flexibleHeaderItems() sync* {
    yield Obx(() => _buildPeriodSelector());
    yield const SizedBox(height: 12);
    yield Obx(() => _buildPeriodNavigation());
    yield const SizedBox(height: 12);
    yield Obx(() => _buildOverviewCard());
  }

  Iterable<Widget> _sliverSectionAItems() sync* {
    yield const SizedBox(height: 16);
    yield Obx(() => _buildChartSelector());
    yield const SizedBox(height: 16);
    yield Obx(() => controller.selectedChartType == ChartType.structure
        ? _buildStructureChart() // 結構圖（已不含分類清單）
        : _buildTrendChart());
    yield const SizedBox(height: 16);
  }

  Iterable<Widget> _sliverSectionCItems() sync* {
    yield Obx(() => _buildDailyTrendChart());
    yield const SizedBox(height: 16);
    yield Obx(() => _buildStatisticsSummary());
    yield const SizedBox(height: 16);
    yield Obx(() => _buildComparison());
    yield const SafeArea(
      top: false,
      child: SizedBox.shrink(),
    );
  }

  Widget _buildPeriodSelector() {
    return CardOverlay(
      alpha: 0.1,
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: Row(
          children: _periodSelectorItems().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _periodSelectorItems() sync* {
    final labels = {'day': '日', 'week': '週', 'month': '月', 'year': '年'};
    for (final period in TimePeriod.values) {
      final isSelected = controller.selectedPeriod == period;
      yield Expanded(
        child: GestureDetector(
          onTap: () => controller.selectPeriod(period),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(Get.context!).cardColor
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              labels[period.name] ?? period.name,
              style: TextStyle(
                color: isSelected
                    ? Theme.of(Get.context!).colorScheme.primary
                    : Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }
  }

  Widget _buildPeriodNavigation() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: _periodNavItems().toList(growable: false),
    );
  }

  Iterable<Widget> _periodNavItems() sync* {
    yield GestureDetector(
      onTap: controller.previousPeriod,
      child: CardOverlay(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        radius: 6,
        alpha: 0.1,
        child: const Icon(
          Icons.chevron_left,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
    yield Text(
      controller.currentPeriodText,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
    );
    yield GestureDetector(
      onTap: controller.nextPeriod,
      child: CardOverlay(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        radius: 6,
        alpha: 0.1,
        child: const Icon(
          Icons.chevron_right,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildOverviewCard() {
    return CardOverlay(
      padding: const EdgeInsets.all(16),
      alpha: 0.1,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _overviewColumnItems().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _overviewColumnItems() sync* {
    yield Text(
      controller.overviewTitle,
      style: TextStyle(
        color: Colors.white.withValues(alpha: 0.8),
        fontSize: 14,
      ),
    );
    yield const SizedBox(height: 12);
    yield Row(
      children: _overviewRowItems().toList(growable: false),
    );
  }

  Iterable<Widget> _overviewRowItems() sync* {
    yield Expanded(
      child: _buildOverviewItem(
        '總收入',
        '+${controller.totalIncome.formattedAmount}',
        ErpColors.incomeLight,
      ),
    );
    yield Expanded(
      child: _buildOverviewItem(
        '總支出',
        '-${controller.totalExpense.formattedAmount}',
        ErpColors.expenseLight,
      ),
    );
    yield Expanded(
      child: _buildOverviewItem(
        '結餘',
        '+${controller.balance.formattedAmount}',
        ErpColors.balanceLight,
      ),
    );
  }

  Widget _buildOverviewItem(String label, String amount, Color color) {
    return Column(
      children:
          _overviewItemChildren(label, amount, color).toList(growable: false),
    );
  }

  Iterable<Widget> _overviewItemChildren(
      String label, String amount, Color color) sync* {
    yield Text(
      label,
      style: TextStyle(
        color: Colors.white.withValues(alpha: 0.8),
        fontSize: 12,
      ),
    );
    yield const SizedBox(height: 4);
    yield Text(
      amount,
      style: TextStyle(
        color: color,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildChartSelector() {
    return CardOverlay(
      padding: const EdgeInsets.all(4),
      child: Row(
        children: _chartSelectorRowItems().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _chartSelectorRowItems() sync* {
    yield Expanded(
      child: GestureDetector(
        onTap: () => controller.selectChartType(ChartType.structure),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: controller.selectedChartType == ChartType.structure
                ? Theme.of(Get.context!).cardColor
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            boxShadow: controller.selectedChartType == ChartType.structure
                ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    )
                  ]
                : null,
          ),
          child: Text(
            '消費結構',
            style: TextStyle(
              color: controller.selectedChartType == ChartType.structure
                  ? Theme.of(Get.context!).colorScheme.onSurface
                  : Theme.of(Get.context!)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.6),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    yield Expanded(
      child: GestureDetector(
        onTap: () => controller.selectChartType(ChartType.trend),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: controller.selectedChartType == ChartType.trend
                ? Theme.of(Get.context!).cardColor
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            boxShadow: controller.selectedChartType == ChartType.trend
                ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    )
                  ]
                : null,
          ),
          child: Text(
            '收支趨勢',
            style: TextStyle(
              color: controller.selectedChartType == ChartType.trend
                  ? Theme.of(Get.context!).colorScheme.onSurface
                  : Theme.of(Get.context!)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.6),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildStructureChart() {
    return CardOverlay(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _structureChartItems().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _structureChartItems() sync* {
    yield const Text(
      '消費結構分析',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
    yield const SizedBox(height: 24);
    yield _buildPieChart();
    // 分離分類清單，改由外層 SliverList 顯示
  }

  Widget _buildPieChart() {
    final sections = controller.categoryData
        .map(
          (c) => PieChartSectionData(
            color: _parseColor(c.color),
            value: c.amount,
            title: '',
            radius: 16,
          ),
        )
        .toList(growable: false);

    return Center(
      child: SizedBox(
        width: 160,
        height: 160,
        child: Stack(
          children: [
            PieChart(
              PieChartData(
                sectionsSpace: 0,
                centerSpaceRadius: 48,
                sections: sections,
              ),
            ),
            Center(
              child: Container(
                width: 96,
                height: 96,
                decoration: BoxDecoration(
                  color: Theme.of(Get.context!).cardColor,
                  shape: BoxShape.circle,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: _pieCenterColumnItems().toList(growable: false),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Iterable<Widget> _pieCenterColumnItems() sync* {
    final theme = Theme.of(Get.context!);
    yield Text(
      '總支出',
      style: TextStyle(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        fontSize: 12,
      ),
    );
    yield const SizedBox(height: 2);
    yield Text(
      controller.totalExpense.formattedAmount,
      style: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildCategoryLegend() {
    final categories = controller.categoryData;
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          // 偶數索引: 顯示項目；奇數索引: 顯示間隔
          if (index.isOdd) {
            return const SizedBox(height: 8);
          }
          final itemIndex = index ~/ 2;
          final category = categories[itemIndex];
          return Material(
            color: Colors.transparent,
            child: CategoryLegendItem(
              category: category,
              onTap: () {
                if (category.id != null) {
                  Get.toNamed(
                    Routes.FILTER_ORDERS,
                    parameters: {
                      'categoryId': category.id.toString(),
                    },
                  );
                }
              },
            ),
          );
        },
        childCount: categories.isEmpty ? 0 : (categories.length * 2 - 1),
      ),
    );
  }

  Widget _buildTrendChart() {
    final dataList = controller.dailyData;
    return CardOverlay(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '收支趨勢分析',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 24),
          if (dataList.isEmpty)
            Center(
              child: Text(
                '暫無資料',
                style: TextStyle(
                  color: Theme.of(Get.context!)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.5),
                  fontSize: 16,
                ),
              ),
            )
          else
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: FlTitlesData(
                    leftTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false)),
                    topTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false)),
                    rightTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false)),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 24,
                        getTitlesWidget: (value, meta) {
                          final i = value.toInt();
                          if (i < 0 || i >= dataList.length) {
                            return const SizedBox.shrink();
                          }
                          final d = dataList[i].date;
                          return Text('${d.day}日',
                              style: TextStyle(
                                  color: Theme.of(Get.context!)
                                      .colorScheme
                                      .onSurface
                                      .withValues(alpha: 0.6),
                                  fontSize: 10));
                        },
                        interval: 1,
                      ),
                    ),
                  ),
                  borderData: FlBorderData(show: false),
                  minX: 0,
                  maxX: (dataList.length - 1).toDouble(),
                  minY: 0,
                  maxY: (dataList
                              .map((e) => e.amount)
                              .fold<double>(0.0, (p, e) => e > p ? e : p) *
                          1.2)
                      .clamp(0.0, double.infinity),
                  lineBarsData: [
                    LineChartBarData(
                      isCurved: true,
                      color: Theme.of(Get.context!).colorScheme.primary,
                      barWidth: 3,
                      dotData: const FlDotData(show: false),
                      spots: [
                        for (int i = 0; i < dataList.length; i++)
                          FlSpot(i.toDouble(), dataList[i].amount),
                      ],
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDailyTrendChart() {
    return CardOverlay(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _dailyTrendColumnItems().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _dailyTrendColumnItems() sync* {
    yield const Text(
      '每日支出趨勢',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
    yield const SizedBox(height: 24);
    yield _buildBarChart();
  }

  Widget _buildBarChart() {
    final dataList = controller.dailyData;
    if (dataList.isEmpty) {
      return Center(
        child: Text(
          '暫無資料',
          style: TextStyle(
            color: Theme.of(Get.context!)
                .colorScheme
                .onSurface
                .withValues(alpha: 0.5),
          ),
        ),
      );
    }
    final maxAmount =
        dataList.map((d) => d.amount).reduce((a, b) => a > b ? a : b);
    return SizedBox(
      height: 160,
      child: BarChart(
        BarChartData(
          gridData: const FlGridData(show: false),
          borderData: FlBorderData(show: false),
          titlesData: FlTitlesData(
            leftTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 24,
                getTitlesWidget: (value, meta) {
                  final i = value.toInt();
                  if (i < 0 || i >= dataList.length) {
                    return const SizedBox.shrink();
                  }
                  final d = dataList[i].date;
                  return Text('${d.day}日',
                      style: TextStyle(
                          color: Theme.of(Get.context!)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.6),
                          fontSize: 10));
                },
                interval: 1,
              ),
            ),
          ),
          barGroups: [
            for (int i = 0; i < dataList.length; i++)
              BarChartGroupData(
                x: i,
                barRods: [
                  BarChartRodData(
                    toY: dataList[i].amount,
                    width: 16,
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(4)),
                    color: Theme.of(Get.context!).colorScheme.primary,
                  ),
                ],
              ),
          ],
          minY: 0,
          maxY: (maxAmount * 1.2).clamp(0.0, double.infinity),
        ),
      ),
    );
  }

  Widget _buildStatisticsSummary() {
    return CardOverlay(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _statisticsSummaryItems().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _statisticsSummaryItems() sync* {
    yield const Text(
      '統計摘要',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
    yield const SizedBox(height: 16);
    yield GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 2.0,
      children: _statGridItems().toList(growable: false),
    );
  }

  Iterable<Widget> _statGridItems() sync* {
    yield _buildStatItem(
      '平均每日支出',
      '\$${controller.averageDailyExpense.formattedAmount}',
      Theme.of(Get.context!).colorScheme.onSurface,
    );
    yield _buildStatItem(
      '最高單日支出',
      '\$${controller.maxDailyExpense.formattedAmount}',
      ErpColors.error,
    );
    yield _buildStatItem(
      '交易次數',
      '${controller.transactionCount} 筆',
      Theme.of(Get.context!).colorScheme.onSurface,
    );
    yield _buildStatItem(
      '最常消費類別',
      controller.topCategory,
      Theme.of(Get.context!).colorScheme.primary,
    );
  }

  Widget _buildStatItem(String label, String value, Color valueColor) {
    return CardOverlay(
      alpha: 0.5,
      radius: 8,
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children:
            _statItemChildren(label, value, valueColor).toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _statItemChildren(
      String label, String value, Color valueColor) sync* {
    yield Text(
      label,
      style: TextStyle(
        fontSize: 12,
        color:
            Theme.of(Get.context!).colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
    yield const SizedBox(height: 4);
    yield Text(
      value,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: valueColor,
      ),
    );
  }

  Widget _buildComparison() {
    return CardOverlay(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _comparisonItems().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _comparisonItems() sync* {
    yield const Text(
      '與上月比較',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
    yield const SizedBox(height: 16);
    yield Column(
      children: _comparisonInnerItems().toList(growable: false),
    );
  }

  Iterable<Widget> _comparisonInnerItems() sync* {
    yield _buildComparisonItem(
      '總支出變化',
      '${controller.expenseChange > 0 ? '+' : ''}${controller.expenseChange.toStringAsFixed(1)}%',
      controller.expenseChange > 0 ? ErpColors.error : ErpColors.success,
      controller.expenseChange > 0 ? Icons.arrow_upward : Icons.arrow_downward,
    );
    yield const SizedBox(height: 12);
    yield _buildComparisonItem(
      '平均每日支出',
      '${controller.avgExpenseChange > 0 ? '+' : ''}${controller.avgExpenseChange.toStringAsFixed(1)}%',
      controller.avgExpenseChange > 0 ? ErpColors.error : ErpColors.success,
      controller.avgExpenseChange > 0
          ? Icons.arrow_upward
          : Icons.arrow_downward,
    );
    yield const SizedBox(height: 12);
    yield _buildComparisonItem(
      '交易次數',
      '${controller.transactionChange > 0 ? '+' : ''}${controller.transactionChange} 筆',
      controller.transactionChange > 0 ? ErpColors.primary : ErpColors.error,
      controller.transactionChange > 0
          ? Icons.arrow_upward
          : Icons.arrow_downward,
    );
  }

  Widget _buildComparisonItem(
      String label, String value, Color color, IconData icon) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: _comparisonItemRowChildren(label, value, color, icon)
          .toList(growable: false),
    );
  }

  Iterable<Widget> _comparisonItemRowChildren(
      String label, String value, Color color, IconData icon) sync* {
    yield Text(
      label,
      style: TextStyle(
        fontSize: 14,
        color:
            Theme.of(Get.context!).colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
    yield Row(
      children: _comparisonValueRowChildren(value, color, icon)
          .toList(growable: false),
    );
  }

  Iterable<Widget> _comparisonValueRowChildren(
      String value, Color color, IconData icon) sync* {
    yield Text(
      value,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: color,
      ),
    );
    yield const SizedBox(width: 8);
    yield Icon(
      icon,
      size: 16,
      color: color,
    );
  }

  Color _parseColor(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    return Color(int.parse('FF$hexColor', radix: 16));
  }
}
