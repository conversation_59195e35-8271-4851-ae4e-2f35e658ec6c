import '../controllers/analysis_controller.dart';
import 'package:get/get.dart';
import '../../../repositories/stats_repository.dart';
import '../../../repositories/category_repository.dart';
import '../../../repositories/order_repository.dart';

class AnalysisBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AnalysisController>(
      () => AnalysisController(
        statsRepository: Get.find<StatsRepository>(),
        categoryRepository: Get.find<CategoryRepository>(),
        orderRepository: Get.find<OrderRepository>(),
      ),
    );
  }
}
