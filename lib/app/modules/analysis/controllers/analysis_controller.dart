import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../repositories/stats_repository.dart';
import '../../../repositories/category_repository.dart';
import '../../../repositories/order_repository.dart';
import '../../../models/erp_category.dart';

enum TimePeriod { day, week, month, year }
enum ChartType { structure, trend }

class CategoryData {
   final int? id;
   final String name;
   final double amount;
   final double percentage;
   final String color;
   final String? icon;

   CategoryData({
     this.id,
     required this.name,
     required this.amount,
     required this.percentage,
     required this.color,
     this.icon,
   });
}

class DailyData {
  final DateTime date;
  final double amount;
  
  DailyData({required this.date, required this.amount});
}

class AnalysisController extends GetxController {
  final StatsRepository statsRepository;
  final CategoryRepository categoryRepository;
  final OrderRepository orderRepository;

  AnalysisController({
    required this.statsRepository,
    required this.categoryRepository,
    required this.orderRepository,
  });
  // 当前选择的时间粒度（私有 Rx）
  final _selectedPeriod = TimePeriod.month.obs;
  TimePeriod get selectedPeriod => _selectedPeriod.value;
  
  // 当前选择的图表类型（私有 Rx）
  final _selectedChartType = ChartType.structure.obs;
  ChartType get selectedChartType => _selectedChartType.value;
  
  // 当前显示的日期（私有 Rx）
  final _currentDate = DateTime.now().obs;
  DateTime get currentDate => _currentDate.value;
  
  // 总览数据（私有 Rx）
  final _totalIncome = 0.0.obs;
  final _totalExpense = 0.0.obs;
  final _balance = 0.0.obs;
  double get totalIncome => _totalIncome.value;
  double get totalExpense => _totalExpense.value;
  double get balance => _balance.value;
  
  // 分类数据（私有 RxList）
  final _categoryData = <CategoryData>[].obs;
  // 對外提供不可變列表，保持封裝；Obx 仍會因 getter 內部觸達 _categoryData 而追蹤
  List<CategoryData> get categoryData => List.unmodifiable(_categoryData);
  
  // 每日数据（私有 RxList）
  final _dailyData = <DailyData>[].obs;
  // 對外提供不可變列表
  List<DailyData> get dailyData => List.unmodifiable(_dailyData);
  
  // 统计摘要数据（私有 Rx）
  final _averageDailyExpense = 0.0.obs;
  final _maxDailyExpense = 0.0.obs;
  final _transactionCount = 0.obs;
  final _topCategory = '-'.obs;
  double get averageDailyExpense => _averageDailyExpense.value;
  double get maxDailyExpense => _maxDailyExpense.value;
  int get transactionCount => _transactionCount.value;
  String get topCategory => _topCategory.value;
  
  // 比较数据（与上一期间）（私有 Rx）
  final _expenseChange = 0.0.obs; // 百分比（正上升，负下降）
  final _avgExpenseChange = 0.0.obs; // 百分比
  final _transactionChange = 0.obs; // 数量差异
  double get expenseChange => _expenseChange.value;
  double get avgExpenseChange => _avgExpenseChange.value;
  int get transactionChange => _transactionChange.value;
  
  // 加载状态（period 变化时显示进度指示器）（私有 Rx）
  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;
  
  @override
  void onInit() {
    super.onInit();
    _loadDataForPeriod();
  }
  
  // 切换时间粒度
  void selectPeriod(TimePeriod period) {
    _selectedPeriod.value = period;
    _loadDataForPeriod();
  }
  
  // 切换图表类型
  void selectChartType(ChartType type) {
    _selectedChartType.value = type;
  }
  
  // 导航到上一个时间段
  void previousPeriod() {
    switch (_selectedPeriod.value) {
      case TimePeriod.day:
        _currentDate.value = _currentDate.value.subtract(const Duration(days: 1));
        break;
      case TimePeriod.week:
        _currentDate.value = _currentDate.value.subtract(const Duration(days: 7));
        break;
      case TimePeriod.month:
        _currentDate.value = DateTime(_currentDate.value.year, _currentDate.value.month - 1, 1);
        break;
      case TimePeriod.year:
        _currentDate.value = DateTime(_currentDate.value.year - 1, _currentDate.value.month, 1);
        break;
    }
    _loadDataForPeriod();
  }
  
  // 导航到下一个时间段
  void nextPeriod() {
    switch (_selectedPeriod.value) {
      case TimePeriod.day:
        _currentDate.value = _currentDate.value.add(const Duration(days: 1));
        break;
      case TimePeriod.week:
        _currentDate.value = _currentDate.value.add(const Duration(days: 7));
        break;
      case TimePeriod.month:
        _currentDate.value = DateTime(_currentDate.value.year, _currentDate.value.month + 1, 1);
        break;
      case TimePeriod.year:
        _currentDate.value = DateTime(_currentDate.value.year + 1, _currentDate.value.month, 1);
        break;
    }
    _loadDataForPeriod();
  }
  
  // 获取当前时间段的显示文本
  String get currentPeriodText {
    switch (_selectedPeriod.value) {
      case TimePeriod.day:
        return DateFormat('yyyy年MM月dd日').format(_currentDate.value);
      case TimePeriod.week:
        final weekStart = _currentDate.value.subtract(Duration(days: _currentDate.value.weekday - 1));
        final weekEnd = weekStart.add(const Duration(days: 6));
        return '${DateFormat('MM/dd').format(weekStart)} - ${DateFormat('MM/dd').format(weekEnd)}';
      case TimePeriod.month:
        return DateFormat('yyyy年MM月').format(_currentDate.value);
      case TimePeriod.year:
        return DateFormat('yyyy年').format(_currentDate.value);
    }
  }
  
  // 获取总览标题
  String get overviewTitle {
    switch (_selectedPeriod.value) {
      case TimePeriod.day:
        return '${DateFormat('MM月dd日').format(_currentDate.value)} 總覽';
      case TimePeriod.week:
        return '本週總覽';
      case TimePeriod.month:
        return '${DateFormat('MM月').format(_currentDate.value)} ${DateFormat('yyyy').format(_currentDate.value)} 總覽';
      case TimePeriod.year:
        return '${DateFormat('yyyy年').format(_currentDate.value)} 總覽';
    }
  }
  
  // 根据时间粒度加载数据（接入真实仓库）
  Future<void> _loadDataForPeriod() async {
    _isLoading.value = true;
    try {
      switch (_selectedPeriod.value) {
        case TimePeriod.month:
          await _loadMonthly();
          break;
        case TimePeriod.day:
          await _loadDaily();
          break;
        case TimePeriod.week:
          // 简化：使用周内每日统计聚合
          await _loadWeekly();
          break;
        case TimePeriod.year:
          // 简化：以月份聚合，后续可优化
          await _loadYearly();
          break;
      }
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _loadMonthly() async {
    final monthStat = await statsRepository.getMonthlyStat(_currentDate.value);
    final expense = monthStat.expenseCents / 100.0;
    final income = monthStat.incomeCents / 100.0;
    _totalExpense.value = expense;
    _totalIncome.value = income;
    _balance.value = income - expense;

    // 分类分布（遍历所有有效分类）
    final categories = await categoryRepository.getAllAsync(includeDeleted: false);
    final List<CategoryData> cats = [];
    double totalExpenseForPercent = expense;
    for (final ErpCategory c in categories) {
      if (c.id == null) continue;
      final cs = await statsRepository.getMonthlyCategoryStat(_currentDate.value, c.id!);
      final amount = cs.expenseCents / 100.0;
      if (amount <= 0) continue;
      cats.add(CategoryData(
        id: c.id,
        name: c.name ?? '未命名',
        amount: amount,
        percentage: 0, // 稍后填充
        color: (c.color ?? '#3B82F6'),
        icon: c.icon,
      ));
    }
    // 计算百分比
    if (totalExpenseForPercent <= 0) {
      for (var i = 0; i < cats.length; i++) {
        cats[i] = CategoryData(
          id: cats[i].id,
          name: cats[i].name,
          amount: cats[i].amount,
          percentage: 0,
          color: cats[i].color,
          icon: cats[i].icon,
        );
      }
    } else {
      for (var i = 0; i < cats.length; i++) {
        final pct = (cats[i].amount / totalExpenseForPercent) * 100.0;
        cats[i] = CategoryData(
          id: cats[i].id,
          name: cats[i].name,
          amount: cats[i].amount,
          percentage: pct,
          color: cats[i].color,
          icon: cats[i].icon,
        );
      }
    }
    // 排序（金额降序）
    cats.sort((a, b) => b.amount.compareTo(a.amount));
    _categoryData.assignAll(cats);

    // 每日趋势（取当月每天）
    final monthStart = DateTime(_currentDate.value.year, _currentDate.value.month, 1);
    final nextMonth = DateTime(monthStart.year, monthStart.month + 1, 1);
    final days = nextMonth.difference(monthStart).inDays;
    final List<DailyData> daily = [];
    double maxDaily = 0.0;
    double sumDaily = 0.0;
    for (int i = 0; i < days; i++) {
      final d = DateTime(monthStart.year, monthStart.month, i + 1);
      final ds = await statsRepository.getDailyStat(d);
      final amt = ds.expenseCents / 100.0;
      daily.add(DailyData(date: d, amount: amt));
      if (amt > maxDaily) maxDaily = amt;
      sumDaily += amt;
    }
    _dailyData.assignAll(daily.take(7).toList()); // UI 只显示7天柱状，保留最近7天

    // 摘要
    _maxDailyExpense.value = maxDaily;
    _averageDailyExpense.value = days > 0 ? (sumDaily / days) : 0.0;
    // 交易次数
    final count = await orderRepository.countWithFilterAsync(
      OrderFilter(
        startDate: monthStart,
        endDate: nextMonth,
        includeDeleted: false,
      ),
    );
    _transactionCount.value = count;
    // 最常类别
    _topCategory.value = cats.isNotEmpty ? cats.first.name : '-';

    // 与上月比较
    final prevMonthStart = DateTime(monthStart.year, monthStart.month - 1, 1);
    final prevStat = await statsRepository.getMonthlyStat(prevMonthStart);
    final prevExpense = prevStat.expenseCents / 100.0;
    _expenseChange.value = _pctChange(prevExpense, expense);

    // 上月平均每日支出
    final prevNext = DateTime(prevMonthStart.year, prevMonthStart.month + 1, 1);
    final prevDays = prevNext.difference(prevMonthStart).inDays;
    double prevSum = 0.0;
    for (int i = 0; i < prevDays; i++) {
      final d = DateTime(prevMonthStart.year, prevMonthStart.month, i + 1);
      final ds = await statsRepository.getDailyStat(d);
      prevSum += ds.expenseCents / 100.0;
    }
    final prevAvg = prevDays > 0 ? (prevSum / prevDays) : 0.0;
    _avgExpenseChange.value = _pctChange(prevAvg, _averageDailyExpense.value);

    // 交易次數變化
    final prevCount = await orderRepository.countWithFilterAsync(
      OrderFilter(
        startDate: prevMonthStart,
        endDate: DateTime(prevMonthStart.year, prevMonthStart.month + 1, 1),
        includeDeleted: false,
      ),
    );
    _transactionChange.value = count - prevCount;
  }

  Future<void> _loadDaily() async {
    final date = DateTime(_currentDate.value.year, _currentDate.value.month, _currentDate.value.day);
    final stat = await statsRepository.getDailyStat(date);
    final expense = stat.expenseCents / 100.0;
    final income = stat.incomeCents / 100.0;
    _totalExpense.value = expense;
    _totalIncome.value = income;
    _balance.value = income - expense;

    // 分类（当天）
    final categories = await categoryRepository.getAllAsync(includeDeleted: false);
    final List<CategoryData> cats = [];
    for (final c in categories) {
      if (c.id == null) continue;
      final cs = await statsRepository.getDailyCategoryStat(date, c.id!);
      final amt = cs.expenseCents / 100.0;
      if (amt <= 0) continue;
      cats.add(CategoryData(
        id: c.id,
        name: c.name ?? '未命名',
        amount: amt,
        percentage: 0,
        color: (c.color ?? '#3B82F6'),
        icon: c.icon,
      ));
    }
    final total = cats.fold<double>(0.0, (p, e) => p + e.amount);
    if (total > 0) {
      for (var i = 0; i < cats.length; i++) {
        final pct = (cats[i].amount / total) * 100.0;
        cats[i] = CategoryData(
          id: cats[i].id,
          name: cats[i].name,
          amount: cats[i].amount,
          percentage: pct,
          color: cats[i].color,
          icon: cats[i].icon,
        );
      }
    }
    cats.sort((a, b) => b.amount.compareTo(a.amount));
    _categoryData.assignAll(cats);

    // 每日趋势：显示最近7天
    final List<DailyData> daily = [];
    for (int i = 6; i >= 0; i--) {
      final d = date.subtract(Duration(days: i));
      final s = await statsRepository.getDailyStat(d);
      daily.add(DailyData(date: d, amount: s.expenseCents / 100.0));
    }
    _dailyData.assignAll(daily);

    // 摘要（基于最近7天）
    if (daily.isNotEmpty) {
      _maxDailyExpense.value = daily.map((e) => e.amount).reduce((a, b) => a > b ? a : b);
      _averageDailyExpense.value = daily.map((e) => e.amount).fold(0.0, (p, e) => p + e) / daily.length;
    } else {
      _maxDailyExpense.value = 0;
      _averageDailyExpense.value = 0;
    }

    // 交易次數（當天）
    final count = await orderRepository.countWithFilterAsync(
      OrderFilter(
        startDate: DateTime(date.year, date.month, date.day),
        endDate: DateTime(date.year, date.month, date.day + 1),
      ),
    );
    _transactionCount.value = count;

    // 与昨天比较
    final y = date.subtract(const Duration(days: 1));
    final ys = await statsRepository.getDailyStat(y);
    _expenseChange.value = _pctChange(ys.expenseCents / 100.0, expense);
    _avgExpenseChange.value = 0; // 日维度无意义，置 0
    final yCount = await orderRepository.countWithFilterAsync(
      OrderFilter(startDate: DateTime(y.year, y.month, y.day), endDate: DateTime(y.year, y.month, y.day + 1)),
    );
    _transactionChange.value = count - yCount;
    _topCategory.value = cats.isNotEmpty ? cats.first.name : '-';
  }

  Future<void> _loadWeekly() async {
    // 以周一开始，计算7天合计
    final weekStart = _currentDate.value.subtract(Duration(days: _currentDate.value.weekday - 1));
    final List<DailyData> daily = [];
    double sum = 0.0;
    double maxV = 0.0;
    for (int i = 0; i < 7; i++) {
      final d = DateTime(weekStart.year, weekStart.month, weekStart.day + i);
      final s = await statsRepository.getDailyStat(d);
      final v = s.expenseCents / 100.0;
      daily.add(DailyData(date: d, amount: v));
      sum += v;
      if (v > maxV) maxV = v;
    }
    _dailyData.assignAll(daily);
    _totalExpense.value = sum;
    _totalIncome.value = 0; // 暂不计算周收入
    _balance.value = -sum;
    _averageDailyExpense.value = sum / 7;
    _maxDailyExpense.value = maxV;
    // 交易次数
    final count = await orderRepository.countWithFilterAsync(OrderFilter(
      startDate: DateTime(weekStart.year, weekStart.month, weekStart.day),
      endDate: DateTime(weekStart.year, weekStart.month, weekStart.day + 7),
    ));
    _transactionCount.value = count;
    // 分类：按周内各分类合计（简单方案：遍历分类并累加每日）
    final categories = await categoryRepository.getAllAsync(includeDeleted: false);
    final Map<int, double> catSum = {};
    for (final c in categories) {
      if (c.id == null) continue;
      double s = 0.0;
      for (int i = 0; i < 7; i++) {
        final d = DateTime(weekStart.year, weekStart.month, weekStart.day + i);
        final cs = await statsRepository.getDailyCategoryStat(d, c.id!);
        s += cs.expenseCents / 100.0;
      }
      if (s > 0) catSum[c.id!] = s;
    }
    final total = catSum.values.fold(0.0, (p, e) => p + e);
    final List<CategoryData> cats = [];
    catSum.forEach((id, amt) {
      final c = categories.firstWhereOrNull((e) => e.id == id);
      cats.add(CategoryData(
        id: id,
        name: c?.name ?? '未命名',
        amount: amt,
        percentage: total > 0 ? (amt / total) * 100.0 : 0,
        color: c?.color ?? '#3B82F6',
        icon: c?.icon,
      ));
    });
    cats.sort((a, b) => b.amount.compareTo(a.amount));
    _categoryData.assignAll(cats);
    _topCategory.value = cats.isNotEmpty ? cats.first.name : '-';

    // 与上周比较（仅总支出、交易数）
    final prevWeekStart = weekStart.subtract(const Duration(days: 7));
    double prevSum = 0.0;
    for (int i = 0; i < 7; i++) {
      final d = DateTime(prevWeekStart.year, prevWeekStart.month, prevWeekStart.day + i);
      final s = await statsRepository.getDailyStat(d);
      prevSum += s.expenseCents / 100.0;
    }
    _expenseChange.value = _pctChange(prevSum, sum);
    final prevCount = await orderRepository.countWithFilterAsync(OrderFilter(
      startDate: DateTime(prevWeekStart.year, prevWeekStart.month, prevWeekStart.day),
      endDate: DateTime(prevWeekStart.year, prevWeekStart.month, prevWeekStart.day + 7),
    ));
    _transactionChange.value = count - prevCount;
    _avgExpenseChange.value = 0; // 先置 0
  }

  Future<void> _loadYearly() async {
    // 按月聚合（12个月）
    final y = DateTime(_currentDate.value.year, 1, 1);
    double sumYear = 0.0;
    final List<DailyData> last7 = [];
    for (int m = 0; m < 12; m++) {
      final d = DateTime(y.year, m + 1, 1);
      final s = await statsRepository.getMonthlyStat(d);
      sumYear += s.expenseCents / 100.0;
    }
    // 使用最近7天作为柱图占位
    for (int i = 6; i >= 0; i--) {
      final d = DateTime.now().subtract(Duration(days: i));
      final s = await statsRepository.getDailyStat(d);
      last7.add(DailyData(date: d, amount: s.expenseCents / 100.0));
    }
    _dailyData.assignAll(last7);
    _totalExpense.value = sumYear;
    _totalIncome.value = 0;
    _balance.value = -sumYear;
    _averageDailyExpense.value = sumYear / 365.0;
    _maxDailyExpense.value = last7.isNotEmpty
        ? last7.map((e) => e.amount).reduce((a, b) => a > b ? a : b)
        : 0.0;
    // 分类：按全年合计（粗略：基于每月分类合并，简单实现为按全部订单类别月合计，需要循环分类）
    final categories = await categoryRepository.getAllAsync(includeDeleted: false);
    final Map<int, double> catSum = {};
    for (final c in categories) {
      if (c.id == null) continue;
      double s = 0.0;
      for (int m = 0; m < 12; m++) {
        final ms = await statsRepository.getMonthlyCategoryStat(DateTime(y.year, m + 1, 1), c.id!);
        s += ms.expenseCents / 100.0;
      }
      if (s > 0) catSum[c.id!] = s;
    }
    final total = catSum.values.fold(0.0, (p, e) => p + e);
    final List<CategoryData> cats = [];
    catSum.forEach((id, amt) {
      final c = categories.firstWhereOrNull((e) => e.id == id);
      cats.add(CategoryData(
        id: id,
        name: c?.name ?? '未命名',
        amount: amt,
        percentage: total > 0 ? (amt / total) * 100.0 : 0,
        color: c?.color ?? '#3B82F6',
        icon: c?.icon,
      ));
    });
    cats.sort((a, b) => b.amount.compareTo(a.amount));
    _categoryData.assignAll(cats);
    _topCategory.value = cats.isNotEmpty ? cats.first.name : '-';

    // 与去年比较（总支出）
    double prevYearSum = 0.0;
    for (int m = 0; m < 12; m++) {
      final s = await statsRepository.getMonthlyStat(DateTime(y.year - 1, m + 1, 1));
      prevYearSum += s.expenseCents / 100.0;
    }
    _expenseChange.value = _pctChange(prevYearSum, sumYear);
    _avgExpenseChange.value = 0;
    _transactionChange.value = 0;
  }

  double _pctChange(double prev, double now) {
    if (prev == 0) return now == 0 ? 0 : 100;
    return ((now - prev) / prev) * 100.0;
  }
}
