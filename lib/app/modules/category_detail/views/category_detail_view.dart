import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/utils/category_colors.dart';
import 'package:pocket_trac/app/utils/category_icons.dart';
import 'package:pocket_trac/extension.dart';

import '../../../../colors.dart';
import '../controllers/category_detail_controller.dart';
import '../../location_picker/widgets/bottom_actions_widget.dart';

class CategoryDetailView extends StatelessWidget {
  const CategoryDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CategoryDetailController>(
      init: CategoryDetailController(
        categoryRepository: Get.find(),
      ),
      builder: (controller) {
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => FocusScope.of(context).unfocus(),
          child: controller.obx(
            (state) => _buildBody(context, controller),
            onLoading: const Center(child: CircularProgressIndicator()),
            onError: (error) => Center(
              child: Text(
                'Error: $error',
                style: const TextStyle(color: ErpColors.error),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建主体内容
  Widget _buildBody(BuildContext context, CategoryDetailController controller) {
    Iterable<Widget> getChildren() sync* {
      yield _buildNameField(context, controller);
      yield const SizedBox(height: 24);
      yield _buildColorSection(context, controller);
      yield const SizedBox(height: 24);
      yield _buildIconSection(context, controller);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children:
          _bodyChildren(context, controller, getChildren).toList(growable: false),
    );
  }

  Iterable<Widget> _bodyChildren(
    BuildContext context,
    CategoryDetailController controller,
    Iterable<Widget> Function() getChildren,
  ) sync* {
    // 表单内容区域
    yield Flexible(
      fit: FlexFit.loose,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: controller.formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: getChildren().toList(growable: false),
          ),
        ),
      ),
    );
    // 底部操作按钮
    yield _buildBottomActions(context, controller);
  }

  /// 构建分类名称输入框
  Widget _buildNameField(BuildContext context, CategoryDetailController controller) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          _nameFieldChildren(textTheme, isDark, controller).toList(growable: false),
    );
  }

  Iterable<Widget> _nameFieldChildren(
    TextTheme textTheme,
    bool isDark,
    CategoryDetailController controller,
  ) sync* {
    yield Text(
      '類別名稱',
      style: TextStyle(
        color: textTheme.bodyLarge?.color ??
            (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
    );
    yield const SizedBox(height: 8);
    yield TextFormField(
      initialValue: controller.draft.name,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '請輸入類別名稱';
        }
        return null;
      },
      onChanged: (value) {
        controller.draft.name = value.trim();
      },
      decoration: const InputDecoration(
        hintText: '輸入類別名稱',
      ),
      style: TextStyle(
        fontSize: 16,
        color: textTheme.bodyLarge?.color ??
            (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
      ),
    );
  }

  /// 构建颜色选择区域
  Widget _buildColorSection(BuildContext context, CategoryDetailController controller) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '選擇顏色',
        style: TextStyle(
          color: textTheme.bodyLarge?.color ??
              (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      );
      yield const SizedBox(height: 12);
      yield Obx(() => _buildColorGrid(context, controller));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  Widget _buildColorGrid(BuildContext context, CategoryDetailController controller) {
    final colors = CategoryColors.availableColors;
    final draft = controller.draft;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GridView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: 50, // 每個顏色圓圈最大寬度
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: colors.length,
      itemBuilder: (context, index) {
        final color = colors[index];
        final isSelected = draft.getColor() == color;

        return GestureDetector(
          onTap: () {
            draft.setColor(color);
            controller.refreshDraft();
          },
          child: Container(
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected
                    ? (isDark ? ErpColors.darkTextPrimary : Colors.white)
                    : Colors.transparent,
                width: 3,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: color.withOpacity(0.5),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
          ),
        );
      },
    );
  }

  /// 构建图标选择区域
  Widget _buildIconSection(BuildContext context, CategoryDetailController controller) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '選擇圖示',
        style: TextStyle(
          color: textTheme.bodyLarge?.color ??
              (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      );
      yield const SizedBox(height: 12);
      yield Obx(() => _buildIconGrid(context, controller));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  Widget _buildIconGrid(BuildContext context, CategoryDetailController controller) {
    final icons = CategoryIcons.availableIcons;
    final draft = controller.draft;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GridView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: 50, // 每個顏色圓圈最大寬度
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: icons.length,
      itemBuilder: (context, index) {
        final icon = icons.elementAt(index);
        final isSelected = draft.getIcon() == icon;

        return GestureDetector(
          onTap: () {
            draft.setIcon(icon);
            controller.refreshDraft();
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected
                  ? ErpColors.primary.withOpacity(0.1)
                  : (isDark ? ErpColors.darkCardBackground : Colors.white),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? ErpColors.primary
                    : (isDark ? ErpColors.darkBorder : ErpColors.border),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Icon(
              icon,
              color: isSelected
                  ? ErpColors.primary
                  : (isDark
                      ? ErpColors.darkTextSecondary
                      : ErpColors.textSecondary),
              size: 24,
            ),
          ),
        );
      },
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions(BuildContext context, CategoryDetailController controller) {
    return BottomActionsWidget(
      onCancel: () => Get.back(),
      onConfirm: controller.saveCategory,
      confirmText: 'save'.tr,
      confirmButtonChild: controller.saveButtonState.obx(
        (state) {
          return Text(
            'save'.tr,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          );
        },
        onLoading: const SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
        onError: (error) => Text(
          'Error: $error',
          style: const TextStyle(color: ErpColors.error),
        ),
      ),
    );
  }
}
