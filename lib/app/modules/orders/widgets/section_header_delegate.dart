import 'package:flutter/material.dart';
import 'package:pocket_trac/app/models/erp_order.dart';
import 'package:pocket_trac/extension.dart';

class SectionHeaderDelegate extends SliverPersistentHeaderDelegate {
  final String title;
  final List<ErpOrder> items;
  final double topPadding;
  final double bottomPadding;
  final double horizontalPadding;

  SectionHeaderDelegate({
    required this.title,
    required this.items,
    this.topPadding = 12,
    this.bottomPadding = 8,
    this.horizontalPadding = 16,
  });

  @override
  double get minExtent => topPadding + bottomPadding + 20; // 近似文字高度

  @override
  double get maxExtent => minExtent;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final theme = Theme.of(context);
    final locale = Localizations.localeOf(context);
    final double elevation = overlapsContent ? 2.0 : 0.0;
    final Color surface = theme.colorScheme.surface;
    final Color dividerColor =
        theme.dividerColor.withValues(alpha: overlapsContent ? 1.0 : 0.0);

    double income = 0.0;
    double expense = 0.0;
    for (final o in items) {
      if (o.isIncome) {
        income += o.amount;
      } else if (o.isExpense) {
        expense += o.amount;
      }
    }
    final balance = income - expense;
    final color = balance >= 0 ? theme.colorScheme.tertiary : theme.colorScheme.error;
    final amountText = (balance >= 0 ? '+ ' : '- ') +
        balance.abs().toCurrency(locale: locale.toString());

    return Material(
      color: Colors.transparent,
      elevation: elevation,
      child: Container(
        padding: EdgeInsets.fromLTRB(
          horizontalPadding,
          topPadding,
          horizontalPadding,
          bottomPadding,
        ),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: surface.withValues(alpha: 0.9),
          border: Border(
            bottom: BorderSide(
              color: dividerColor.withValues(alpha: 0.9),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: _rowChildren(amountText, color).toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _rowChildren(String amountText, Color color) sync* {
    yield Expanded(
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
    yield const SizedBox(width: 12);
    yield Text(
      amountText,
      style: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w600,
        color: color,
      ),
    );
  }

  @override
  bool shouldRebuild(covariant SectionHeaderDelegate oldDelegate) {
    return title != oldDelegate.title ||
        topPadding != oldDelegate.topPadding ||
        bottomPadding != oldDelegate.bottomPadding ||
        horizontalPadding != oldDelegate.horizontalPadding ||
        !identical(items, oldDelegate.items) ||
        items.length != oldDelegate.items.length;
  }
}
