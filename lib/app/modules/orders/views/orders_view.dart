import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/routes/app_pages.dart';
import 'package:pocket_trac/app/widgets/gradient_background.dart';
import 'package:pocket_trac/keys.dart';
import 'package:pocket_trac/app/widgets/order_item.dart';
import 'package:pocket_trac/app/models/erp_order.dart';
import 'package:pocket_trac/app/modules/orders/widgets/section_header_delegate.dart';

import '../controllers/orders_controller.dart';

class OrdersView extends GetView<OrdersController> {
  const OrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    controller.talker.debug('OrdersView build');

    return Scaffold(
      body: SafeArea(
        child: controller.obx(
          (state) => Obx(() => GradientBackground(
                child: _buildContent(context),
              )),
          onLoading: const Center(
            child: CircularProgressIndicator(),
          ),
          onEmpty: _buildEmptyState(context),
          onError: (error) => Center(
            child: Text(error.toString()),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _confirmDialogActions(BuildContext ctx) sync* {
    yield TextButton(
      onPressed: () => Navigator.of(ctx).pop(false),
      child: Text('cancel'.tr),
    );
    yield FilledButton(
      onPressed: () => Navigator.of(ctx).pop(true),
      child: Text('delete'.tr),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: _emptyStateChildren(context).toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _emptyStateChildren(BuildContext context) sync* {
    yield Icon(
      Icons.receipt_long,
      size: 64,
      color: Colors.grey[400],
    );
    yield const SizedBox(height: 16);
    yield Text(
      'transactions_no_data'.tr,
      style: TextStyle(
        fontSize: 16,
        color: Colors.grey[600],
      ),
    );
    yield const SizedBox(height: 20);
    yield FilledButton.icon(
      onPressed: () => Get.toNamed(Routes.ORDER_DETAIL),
      icon: const Icon(Icons.add),
      label: Text('transactions_add'.tr),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    final surface = theme.colorScheme.surface;
    final dividerColor = theme.dividerColor;
    return SliverAppBar(
      floating: true,
      snap: true,
      backgroundColor: surface.withValues(alpha: 0.9),
      foregroundColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
      elevation: 0,
      scrolledUnderElevation: 0,
      shape: Border(
        bottom: BorderSide(
          color: dividerColor.withValues(alpha: 0.9),
          width: 0.5,
        ),
      ),
      title: Material(
        color: Colors.transparent,
        child: Text(
          'transactions'.tr,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      actions: _appBarActions(context).toList(growable: false),
    );
  }

  Iterable<Widget> _appBarActions(BuildContext context) sync* {
    yield IconButton(
      onPressed: () => Get.toNamed(Routes.ANALYSIS),
      icon: const Icon(Icons.analytics_outlined),
      tooltip: 'analysis'.tr,
    );
    yield const SizedBox(width: 8);
  }

  Widget _buildContent(BuildContext context) {
    final orders = controller.orders;
    final groups = controller.groupedOrders;

    if (orders.isEmpty) {
      return _buildEmptyState(context);
    }

    // 依插入順序保留分組順序（orders 已為新到舊）
    final sectionKeys = groups.keys.toList();

    // 是否還有更多資料（用於底部 Loading）
    final hasMore = controller.hasMoreData;

    // 使用 Slivers 以支援吸頂區塊標題
    return CustomScrollView(
      controller: controller.scroll,
      slivers: _sliversWrapper(
        context: context,
        sectionKeys: sectionKeys,
        groups: groups,
        hasMore: hasMore,
      ).toList(growable: false),
    );
  }

  Iterable<Widget> _sliversWrapper({
    required BuildContext context,
    required List<String> sectionKeys,
    required Map<String, List<ErpOrder>> groups,
    required bool hasMore,
  }) sync* {
    yield _buildAppBar(context);
    yield* _buildSlivers(
      sectionKeys: sectionKeys,
      groups: groups,
      hasMore: hasMore,
      context: context,
    );
  }

  Iterable<Widget> _buildSlivers({
    required List<String> sectionKeys,
    required Map<String, List<ErpOrder>> groups,
    required bool hasMore,
    required BuildContext context,
  }) sync* {
    for (int s = 0; s < sectionKeys.length; s++) {
      final key = sectionKeys[s];
      final items = groups[key] ?? const <ErpOrder>[];
      yield _buildSection(key, items);
    }

    // 底部空間 + 分頁 loading 指示
    if (hasMore) {
      yield SliverToBoxAdapter(
        child: Obx(() {
          return controller.isLoadingMore
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: CircularProgressIndicator(),
                  ),
                )
              : const SizedBox(height: 40);
        }),
      );
    } else {
      yield const SliverToBoxAdapter(child: SizedBox(height: 40));
    }
  }

  Widget _buildSection(String title, List<ErpOrder> items) {
    return SliverMainAxisGroup(
      slivers: [
        _buildSectionHeader(title, items),
        _buildOrderList(items),
      ],
    );
  }

  SliverPersistentHeader _buildSectionHeader(
      String title, List<ErpOrder> items) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: SectionHeaderDelegate(
        title: title,
        items: items,
        topPadding: 12,
        bottomPadding: 8,
        horizontalPadding: 16,
      ),
    );
  }

  SliverList _buildOrderList(List<ErpOrder> items) {
    // 使用分隔占位避免項目之間的 padding 疊加造成 2 倍間距
    // 並在列表頭尾各加入 8px 占位，確保首尾也有一致間距。
    // 形如: topSpacer, item, separator, item, ..., item, bottomSpacer
    if (items.isEmpty) {
      return SliverList(
        delegate: SliverChildListDelegate(const []),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, idx) {
          // 索引 0: 頂部占位
          if (idx == 0) {
            return const SizedBox(height: 8);
          }

          // 調整索引使後續計算以 0 開始
          final adjusted = idx - 1;

          // 最後一個索引: 底部占位
          final lastIndex = (items.length * 2 - 1);
          if (adjusted == lastIndex) {
            return const SizedBox(height: 8);
          }

          // 奇數為分隔線
          if (adjusted.isOdd) {
            return const SizedBox(height: 8);
          }

          // 偶數為實際項目
          final itemIndex = adjusted ~/ 2;
          final order = items[itemIndex];
          return _buildOrderListItem(context, order, itemIndex);
        },
        // N 個項目 -> top(1) + 2N-1(交錯) + bottom(1) = 2N+1 個 child
        childCount: items.length * 2 + 1,
      ),
    );
  }

  Widget _buildOrderListItem(BuildContext context, ErpOrder order, int idx) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Dismissible(
        key: ValueKey('order_${order.id ?? order.objectId ?? idx}'),
        direction: DismissDirection.endToStart,
        background: _buildDismissibleStartBg(context),
        secondaryBackground: _buildDismissibleBg(context),
        confirmDismiss: (_) async {
          return await showDialog<bool>(
                context: context,
                builder: (ctx) => AlertDialog(
                  title: Text('confirm_delete_title'.tr),
                  content: Text('confirm_delete_message'.tr),
                  actions: _confirmDialogActions(ctx).toList(growable: false),
                ),
              ) ??
              false;
        },
        onDismissed: (_) async {
          // Cache messenger to avoid using BuildContext across async gaps
          final messenger = ScaffoldMessenger.of(context);
          final ok = await controller.deleteOrder(order, hard: false);
          if (!ok) {
            messenger.showSnackBar(
              SnackBar(content: Text('delete_failed'.tr)),
            );
            await controller.loadInitial();
          } else {
            messenger.hideCurrentSnackBar();
            messenger.showSnackBar(
              SnackBar(
                content: Text('delete_success'.tr),
                action: SnackBarAction(
                  label: 'undo'.tr,
                  onPressed: () async {
                    final restored = await controller.restoreOrder(order);
                    if (!restored) {
                      messenger.showSnackBar(
                        SnackBar(content: Text('undo_failed'.tr)),
                      );
                    } else {
                      messenger.showSnackBar(
                        SnackBar(content: Text('undo_success'.tr)),
                      );
                    }
                  },
                ),
                duration: const Duration(seconds: 4),
              ),
            );
          }
        },
        child: Material(
          color: Colors.transparent,
          child: OrderItem(
            order: order,
            onTap: () {
              Get.toNamed(
                Routes.ORDER_DETAIL,
                parameters: {Keys.id: '${order.id ?? 0}'},
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildDismissibleBg(BuildContext context) {
    final error = Theme.of(context).colorScheme.error;
    return Container(
      alignment: Alignment.centerRight,
      padding: const EdgeInsets.symmetric(horizontal: 24),
      decoration: BoxDecoration(
        color: error.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(Icons.delete, color: error),
    );
  }

  Widget _buildDismissibleStartBg(BuildContext context) {
    final error = Theme.of(context).colorScheme.error;
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 24),
      decoration: BoxDecoration(
        color: error.withValues(alpha: 0.04),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(Icons.delete, color: error.withValues(alpha: 0.8)),
    );
  }
}
