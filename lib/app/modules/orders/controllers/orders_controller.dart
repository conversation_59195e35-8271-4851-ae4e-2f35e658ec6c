import 'dart:async';
import 'package:get/get.dart';
import 'package:pocket_trac/app/models/erp_order.dart';
import 'package:pocket_trac/extension.dart';
import 'package:pocket_trac/app/providers/box_provider.dart';
import 'package:pocket_trac/app/providers/pref_provider.dart';
import 'package:pocket_trac/app/repositories/order_repository.dart';
import 'package:talker_flutter/talker_flutter.dart';

class OrdersController extends GetxController
    with StateMixin<List<ErpOrder>>, ScrollMixin {
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Talker get talker => boxProvider.talker;

  // Repository
  final OrderRepository orderRepository;
  final PrefProvider prefProvider;

  // Observable data
  final orders = <ErpOrder>[].obs;
  final groupedOrders = <String, List<ErpOrder>>{}.obs;
  final searchQuery = ''.obs;
  final selectedFilter = OrderFilter().obs;

  // Statistics
  final totalIncome = 0.0.obs;
  final totalExpense = 0.0.obs;
  final totalBalance = 0.0.obs;

  // Pagination
  final _currentPage = 0.obs;
  int get currentPage => _currentPage.value;
  final _hasMoreData = true.obs;
  bool get hasMoreData => _hasMoreData.value;
  final _isLoadingMore = false.obs;
  bool get isLoadingMore => _isLoadingMore.value;

  // Stream subscription for live updates
  StreamSubscription<List<ErpOrder>>? _ordersSub;

  OrdersController({
    required this.orderRepository,
    required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 防止熱重載後仍有舊的 stream 訂閱覆寫列表
    _ordersSub?.cancel();
    change(null, status: RxStatus.loading());
    // 方案 A：改為純游標初始載入
    unawaited(loadInitial());
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    _ordersSub?.cancel();
  }

  // 已改為使用資料流，即時更新列表

  /// 刷新數據（重新綁定 stream，會觸發 triggerImmediately）
  Future<void> onRefresh() async {
    await loadInitial();
  }

  /// 方案 A：以游標方式載入首屏資料（不使用 stream）
  Future<void> loadInitial({int limit = 50}) async {
    try {
      change(null, status: RxStatus.loading());

      // 重置狀態
      orders.clear();
      groupedOrders.clear();
      totalIncome.value = 0.0;
      totalExpense.value = 0.0;
      totalBalance.value = 0.0;
      _currentPage.value = 0;
      _hasMoreData.value = true;

      // 以「現在時間 + 極大 id」作為初始游標，抓取最新一頁
      final nowMs = DateTime.now().millisecondsSinceEpoch;
      const maxId = 1 << 62; // 足夠大的 id 以涵蓋現有資料

      final firstPage = await orderRepository.getOrdersBeforeWithFilter(
        selectedFilter.value,
        cursorTriggerAtMs: nowMs,
        cursorId: maxId,
        limit: limit,
      );

      orders.assignAll(firstPage);
      _groupOrdersByDate();
      _calculateStatistics();

      _hasMoreData.value = firstPage.length >= limit;
      change(firstPage, status: RxStatus.success());
      talker.debug('Initial page loaded: ${firstPage.length} orders');
      // 啟動最近資料監聽，保持列表頂部即時更新
      _startRecentWatch(limit: limit);
    } catch (e, s) {
      talker.error('Failed to load initial orders: $e', e, s);
      change(null, status: RxStatus.error('載入訂單失敗: $e'));
    }
  }

  /// 載入更多數據（提供給 ScrollMixin 的 onEndScroll 觸發）
  @override
  Future<void> onEndScroll() async {
    await _loadMore();
  }

  /// 置頂滑動（目前暫不處理）
  @override
  Future<void> onTopScroll() async {}

  /// 滑動過程（可用於除錯日誌）
  void onScroll() {
    try {
      final p = scroll.position.pixels;
      final m = scroll.position.maxScrollExtent;
      if (m > 0 && (m - p) <= 200) {
        // 靠近底部時提前觸發一次，降低到頂後才觸底的閃爍
        unawaited(_loadMore());
      }
    } catch (_) {}
  }

  /// 內部實作：實際載入更多
  Future<void> _loadMore() async {
    if (_isLoadingMore.value || !_hasMoreData.value) return;

    try {
      _isLoadingMore.value = true;
      talker.debug(
          '[OrdersController] onEndScroll() start: len=${orders.length}, hasMore=${_hasMoreData.value}');

      // 使用游標（以目前列表最後一筆作為游標）
      if (orders.isEmpty) {
        talker.debug('[OrdersController] onEndScroll() skipped: orders empty');
        _isLoadingMore.value = false;
        return;
      }

      final last = orders.last;
      final lastTriggerAt =
          (last.triggerAt ?? DateTime.fromMillisecondsSinceEpoch(0))
              .millisecondsSinceEpoch;
      final lastId = last.id ?? 0;
      talker.debug(
          '[OrdersController] cursor lastTriggerAtMs=$lastTriggerAt, lastId=$lastId');

      final moreOrders = await orderRepository.getOrdersBeforeWithFilter(
        selectedFilter.value,
        cursorTriggerAtMs: lastTriggerAt,
        cursorId: lastId,
        limit: 20,
      );

      if (moreOrders.isNotEmpty) {
        orders.addAll(moreOrders);
        _groupOrdersByDate();
        _calculateStatistics();
        _currentPage.value = _currentPage.value + 1;
        _hasMoreData.value = moreOrders.length >= 20;
        talker.debug(
            '[OrdersController] loaded more: fetched=${moreOrders.length}, newLen=${orders.length}, page=$currentPage, hasMore=$hasMoreData');
      } else {
        _hasMoreData.value = false;
        talker.debug('[OrdersController] no more data. Set hasMore=false');
      }

      talker.debug('Loaded ${moreOrders.length} more orders');
    } catch (e, s) {
      talker.error('Failed to load more orders: $e', e, s);
    } finally {
      _isLoadingMore.value = false;
      talker.debug('[OrdersController] onEndScroll() end: isLoadingMore=false');
    }
  }

  /// 按日期分組訂單
  void _groupOrdersByDate() {
    final Map<String, List<ErpOrder>> grouped = {};

    for (final order in orders) {
      final date = order.triggerAt;
      if (date != null) {
        final dateKey = _getDateKey(date);
        grouped.putIfAbsent(dateKey, () => []);
        grouped[dateKey]!.add(order);
      }
    }

    // 按日期排序每組內的訂單
    grouped.forEach((key, orderList) {
      orderList.sort((a, b) {
        final aTime = a.triggerAt ?? DateTime.now();
        final bTime = b.triggerAt ?? DateTime.now();
        return bTime.compareTo(aTime); // 最新的在前
      });
    });

    groupedOrders.assignAll(grouped);
  }

  /// 獲取日期分組的鍵值
  String _getDateKey(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final orderDate = DateTime(date.year, date.month, date.day);

    if (orderDate == today) {
      return 'transactions_today'.tr;
    } else if (orderDate == yesterday) {
      return 'transactions_yesterday'.tr;
    } else {
      return '${date.month}/${date.day}';
    }
  }

  /// 計算統計數據
  void _calculateStatistics() {
    double income = 0.0;
    double expense = 0.0;

    for (final order in orders) {
      if (order.isIncome) {
        income += order.amount;
      } else if (order.isExpense) {
        expense += order.amount;
      }
    }

    totalIncome.value = income;
    totalExpense.value = expense;
    totalBalance.value = income - expense;
  }

  // 方案 A：已移除首屏 stream 綁定，統一用游標式載入

  /// 啟動最近 N 筆資料監聽，與現有列表合併，保持頂部最新
  void _startRecentWatch({int limit = 50}) {
    try {
      _ordersSub?.cancel();
      _ordersSub = orderRepository
          .watchRecentWithFilter(selectedFilter.value, limit: limit)
          .listen((recent) {
        // 以 id 去重，recent 放在頂部，保留既有非 recent 的舊資料以支援分頁
        final recentIds = recent.map((e) => e.id).toSet();

        // 取出不在 recent 中的既有資料（舊資料）
        final tail = orders.where((o) => !recentIds.contains(o.id)).toList();

        // 合併並指派（保持排序：recent 已為 triggerAt desc, id desc）
        final merged = <ErpOrder>[...recent, ...tail];
        // 注意：僅以長度與首項 id 判斷“無變化”會導致內容更新（例如金額、備註變更）不被刷新，
        // 尤其在只有一筆資料時更明顯。因此移除此早退，總是指派合併結果以確保 UI 更新。
        orders.assignAll(merged);
        _groupOrdersByDate();
        _calculateStatistics();
      });
      talker.debug('Recent watch started (limit: $limit)');
    } catch (e, s) {
      talker.error('Failed to start recent watch: $e', e, s);
    }
  }

  /// 搜尋訂單
  void searchOrders(String query) {
    searchQuery.value = query;
    final filter = selectedFilter.value.copyWith(
      noteSearch: query.isEmpty ? null : query,
    );
    selectedFilter.value = filter;
    unawaited(loadInitial());
  }

  /// 應用篩選
  void applyFilter(OrderFilter filter) {
    selectedFilter.value = filter;
    unawaited(loadInitial());
  }

  /// 清除篩選
  void clearFilter() {
    selectedFilter.value = const OrderFilter();
    searchQuery.value = '';
    unawaited(loadInitial());
  }

  /// 刪除單筆訂單（預設為軟刪除），並同步更新本地狀態
  Future<bool> deleteOrder(ErpOrder order, {bool hard = false}) async {
    try {
      final id = order.id;
      if (id == null) {
        talker.warning('[OrdersController] deleteOrder: order.id is null');
        return false;
      }

      final ok = hard
          ? await orderRepository.hardDeleteAsync(id)
          : await orderRepository.softDeleteAsync(id);

      if (!ok) return false;

      // 立即在本地移除，避免等 stream 才更新造成體感延遲
      orders.removeWhere((o) => o.id == id);
      _groupOrdersByDate();
      _calculateStatistics();

      talker
          .debug('[OrdersController] deleteOrder success (hard=$hard, id=$id)');
      return true;
    } catch (e, s) {
      talker.error('Failed to delete order: $e', e, s);
      return false;
    }
  }

  /// 撤銷刪除（還原軟刪除），並同步更新本地狀態
  Future<bool> restoreOrder(ErpOrder order) async {
    try {
      final id = order.id;
      if (id == null) {
        talker.warning('[OrdersController] restoreOrder: order.id is null');
        return false;
      }

      final ok = await orderRepository.restoreAsync(id);
      if (!ok) return false;

      // 加回列表：若列表已有同 id，避免重複，交由 stream 合併即可
      final exists = orders.any((o) => o.id == id);
      if (!exists) {
        // 插入頂部以獲得立即體感；排序由 _groupOrdersByDate 處理
        orders.insert(0, order);
      }
      // 保持排序與統計（即使存在，也確保重算；stream 到時也會同步）
      _groupOrdersByDate();
      _calculateStatistics();

      talker.debug('[OrdersController] restoreOrder success (id=$id)');
      return true;
    } catch (e, s) {
      talker.error('Failed to restore order: $e', e, s);
      return false;
    }
  }
}
