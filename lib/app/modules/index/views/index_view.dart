import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/routes/app_pages.dart';

import '../controllers/index_controller.dart';
import '../../home/<USER>/home_view.dart';
import '../../orders/views/orders_view.dart';
import '../../orders/controllers/orders_controller.dart';
import '../../analysis/views/analysis_view.dart';
import '../../categories/views/categories_view.dart';
import '../../settings/views/settings_view.dart';

class IndexView extends GetView<IndexController> {
  const IndexView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: Obx(() => _getCurrentPage()),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Get.toNamed(Routes.ORDER_DETAIL),
        tooltip: 'transactions_add'.tr,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        child: const Icon(Icons.add),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: Obx(
        () => BottomAppBar(
          shape: const CircularNotchedRectangle(),
          notchMargin: 6.0,
          color: theme.colorScheme.surface,
          elevation: 8,
          child: SizedBox(
            height: 60,
            child: Row(
              children: _bottomBarChildren(theme).toList(growable: false),
            ),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _bottomBarChildren(ThemeData theme) sync* {
    yield Expanded(
      child: IconButton(
        iconSize: 26,
        tooltip: 'nav_transactions'.tr,
        color: controller.currentTab == TabIndex.orders
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface.withValues(alpha: 0.6),
        icon: const Icon(Icons.list),
        onPressed: () {
          if (controller.currentTab == TabIndex.orders) {
            // 再次點擊：滾動到頂部
            try {
              final oc = Get.find<OrdersController>();
              if (oc.scroll.hasClients) {
                oc.scroll.animateTo(
                  0,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            } catch (_) {
              // 忽略：若未註冊（理論上在 orders 頁面時應已存在）
            }
          } else {
            controller.changeTab(TabIndex.orders);
          }
        },
      ),
    );
    yield const Spacer();
    yield Expanded(
      child: IconButton(
        iconSize: 26,
        tooltip: 'nav_settings'.tr,
        color: controller.currentTab == TabIndex.settings
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface.withValues(alpha: 0.6),
        icon: const Icon(Icons.settings),
        onPressed: () => controller.changeTab(TabIndex.settings),
      ),
    );
  }

  Widget _getCurrentPage() {
    switch (controller.currentTab) {
      case TabIndex.home:
        return const HomeView();
      case TabIndex.orders:
        return OrdersView();
      case TabIndex.analysis:
        return const AnalysisView();
      case TabIndex.categories:
        return const CategoriesView();
      case TabIndex.settings:
        return const SettingsView();
    }
  }
}
