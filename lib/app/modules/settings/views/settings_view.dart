import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/widgets/card_overlay.dart';
import 'package:pocket_trac/app/widgets/gradient_background.dart';
import 'package:pocket_trac/colors.dart';
import 'package:pocket_trac/extension.dart';
import 'package:pocket_trac/app/routes/app_pages.dart';
import '../controllers/settings_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: controller.obx(
        (state) => GradientBackground(
          child: CustomScrollView(
            slivers: _slivers(context).toList(growable: false),
          ),
        ),
        onLoading: const Center(
          child: CircularProgressIndicator(),
        ),
        onError: (error) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: _errorChildren(error).toList(growable: false),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _slivers(BuildContext context) sync* {
    final theme = Theme.of(context);
    yield SliverLayoutBuilder(
      builder: (context, constraints) {
        const double expandedH = 136;
        final bool isCollapsed =
            constraints.scrollOffset > (expandedH - kToolbarHeight);
        return SliverAppBar(
          pinned: true,
          floating: false,
          snap: false,
          expandedHeight: expandedH,
          elevation: 0,
          scrolledUnderElevation: 0,
          automaticallyImplyLeading: false,
          centerTitle: true,
          title: isCollapsed ? Text('設定') : null,
          flexibleSpace: FlexibleSpaceBar(
            titlePadding: const EdgeInsetsDirectional.only(
                start: 16, bottom: 12, end: 16),
            collapseMode: CollapseMode.parallax,
            background: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: theme.brightness == Brightness.dark
                      ? [ErpColors.darkGradientStart, ErpColors.darkGradientEnd]
                      : [ErpColors.gradientStart, ErpColors.gradientEnd],
                ),
              ),
              child: SafeArea(
                bottom: false,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: _buildHeaderContent().toList(growable: false),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );

    yield SliverPadding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 40),
      sliver: SliverList(
        delegate: SliverChildListDelegate(
          _buildMainContent().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _userInfoColumnChildren() sync* {
    yield const Text(
      '張小明',
      style: TextStyle(
        color: Colors.white,
        fontSize: 18,
        fontWeight: FontWeight.w500,
      ),
    );
    yield const SizedBox(height: 4);
    yield const Text(
      '<EMAIL>',
      style: TextStyle(
        color: Colors.white70,
        fontSize: 14,
      ),
    );
    yield const SizedBox(height: 2);
    yield const Text(
      '已同步：2024/03/15 14:30',
      style: TextStyle(
        color: Colors.white60,
        fontSize: 12,
      ),
    );
  }

  Iterable<Widget> _errorChildren(Object? error) sync* {
    yield Text(
      '載入失敗',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: Theme.of(Get.context!).colorScheme.onSurface,
      ),
    );
    yield const SizedBox(height: 8);
    yield Text(
      error.toString(),
      style: TextStyle(
        color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
      ),
    );
    yield const SizedBox(height: 16);
    yield ElevatedButton(
      onPressed: () => controller.onRefresh(),
      child: const Text('重試'),
    );
  }

  Iterable<Widget> _buildHeaderContent() sync* {
    // User info card
    yield CardOverlay(
      alpha: 0.1,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: _buildUserInfoRowContent().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _buildUserInfoRowContent() sync* {
    // User avatar
    yield Container(
      width: 64,
      height: 64,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(32),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 32,
      ),
    );

    yield const SizedBox(width: 12);

    // User info
    yield Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _userInfoColumnChildren().toList(growable: false),
      ),
    );

    yield IconButton(
      onPressed: () {
        // Edit profile functionality
      },
      icon: const Icon(
        Icons.edit,
        color: Colors.white70,
        size: 20,
      ),
    );
  }

  Iterable<Widget> _buildMainContent() sync* {
    yield _buildDataManagementSection();
    yield const SizedBox(height: 24);
    yield _buildAppSettingsSection();
    yield const SizedBox(height: 24);
    yield _buildSecurityPrivacySection();
    yield const SizedBox(height: 24);
    yield _buildAboutSection();
    yield const SizedBox(height: 24);
    yield _buildLogoutButton();
    yield const SizedBox(height: 16);
    yield _buildCopyright();
  }

  Widget _buildDataManagementSection() {
    return _buildSection(
      title: '資料管理',
      items: _buildDataManagementItems().toList(growable: false),
    );
  }

  Iterable<Widget> _buildDataManagementItems() sync* {
    yield _buildSettingItem(
      icon: Icons.cloud_sync,
      iconColor: Colors.blue,
      title: '雲端同步',
      subtitle: '自動備份至雲端',
      trailing: _buildToggleSwitch(true),
    );

    yield _buildSettingItem(
      icon: Icons.backup,
      iconColor: Colors.green,
      title: '匯出資料',
      subtitle: '匯出至 JSON',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () async {
        await showDialog<bool>(
          context: Get.context!,
          builder: (ctx) => AlertDialog(
            title: const Text('匯出資料'),
            content: const Text('選擇匯出位置'),
            actions: _exportDialogActions(ctx).toList(growable: false),
          ),
        );
      },
    );

    yield _buildSettingItem(
      icon: Icons.file_download,
      iconColor: Colors.purple,
      title: '匯入資料',
      subtitle: '匯入 JSON 格式',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () async {
        // Cache messenger before async work to avoid using context across async gaps
        final messenger = ScaffoldMessenger.of(Get.context!);
        // Show loading dialog (non-blocking)
        Get.showLoading();
        try {
          final msg = await controller.importData();
          Get.back();
          if (msg != null) {
            messenger.hideCurrentSnackBar();
            messenger.showSnackBar(
              SnackBar(
                content: Text(msg),
                duration: const Duration(seconds: 4),
              ),
            );
          }
        } catch (e) {
          Get.back();
          messenger.showSnackBar(
            SnackBar(
              content: Text('匯入失敗：$e'),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      },
    );

    yield _buildSettingItem(
      icon: Icons.delete_forever,
      iconColor: Colors.red,
      title: '清除資料',
      subtitle: '刪除所有本地資料',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        showDialog<bool>(
          context: Get.context!,
          builder: (ctx) => AlertDialog(
            title: const Text('確認清除資料'),
            content: const Text('此操作將刪除所有本地資料，且無法復原，是否繼續？'),
            actions: _clearDataDialogActions(ctx).toList(growable: false),
          ),
        );
      },
    );
  }

  Widget _buildAppSettingsSection() {
    return _buildSection(
      title: 'App 設定',
      items: _buildAppSettingsItems().toList(growable: false),
    );
  }

  Iterable<Widget> _buildAppSettingsItems() sync* {
    yield Obx(() => _buildSettingItem(
          icon: Icons.language,
          iconColor: Colors.grey,
          title: 'settings_language'.tr,
          subtitle: controller.prefProvider.currentLocaleDisplayName,
          trailing: const Icon(Icons.chevron_right, color: Colors.grey),
          onTap: showLanguageDialog,
        ));

    yield _buildSettingItem(
      icon: Icons.category,
      iconColor: Colors.teal,
      title: '類別列表',
      subtitle: '管理/新增/編輯/刪除類別',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () => Get.toNamed(Routes.CATEGORIES),
    );

    yield _buildSettingItem(
      icon: Icons.notifications,
      iconColor: Colors.orange,
      title: 'settings_notifications'.tr,
      subtitle: 'settings_notifications_subtitle'.tr,
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Notification settings
      },
    );

    yield Obx(() => _buildSettingItem(
          icon: Icons.palette,
          iconColor: Colors.green,
          title: 'settings_theme'.tr,
          subtitle: controller.prefProvider.currentThemeMode.display,
          trailing: const Icon(Icons.chevron_right, color: Colors.grey),
          onTap: showThemeDialog,
        ));

    yield _buildSettingItem(
      icon: Icons.currency_exchange,
      iconColor: Colors.blue,
      title: 'settings_currency'.tr,
      subtitle: 'settings_currency_subtitle'.tr,
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Currency settings
      },
    );
  }

  Widget _buildSecurityPrivacySection() {
    return _buildSection(
      title: '安全與隱私',
      items: _buildSecurityPrivacyItems().toList(growable: false),
    );
  }

  Iterable<Widget> _buildSecurityPrivacyItems() sync* {
    yield _buildSettingItem(
      icon: Icons.fingerprint,
      iconColor: Colors.blue,
      title: 'Touch ID / Face ID',
      subtitle: '使用生物辨識解鎖',
      trailing: _buildToggleSwitch(true),
    );

    yield _buildSettingItem(
      icon: Icons.lock,
      iconColor: Colors.orange,
      title: '密碼設定',
      subtitle: '變更應用程式密碼',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Password settings
      },
    );

    yield _buildSettingItem(
      icon: Icons.privacy_tip,
      iconColor: Colors.purple,
      title: '隱私權設定',
      subtitle: '管理個人資料使用',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Privacy settings
      },
    );
  }

  Widget _buildAboutSection() {
    return _buildSection(
      title: '關於',
      items: _buildAboutItems().toList(growable: false),
    );
  }

  Iterable<Widget> _buildAboutItems() sync* {
    yield _buildSettingItem(
      icon: Icons.info,
      iconColor: Colors.blue,
      title: '版本資訊',
      subtitle: 'v1.0.0 (Build 1)',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Version info
      },
    );

    yield _buildSettingItem(
      icon: Icons.help,
      iconColor: Colors.green,
      title: '使用說明',
      subtitle: '查看操作指南',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Help guide
      },
    );

    yield _buildSettingItem(
      icon: Icons.feedback,
      iconColor: Colors.orange,
      title: '意見回饋',
      subtitle: '回報問題或建議',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Feedback
      },
    );

    yield _buildSettingItem(
      icon: Icons.gavel,
      iconColor: Colors.purple,
      title: '服務條款',
      subtitle: '使用條款與政策',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Terms of service
      },
    );
  }

  Widget _buildSection({required String title, required List<Widget> items}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _sectionChildren(title, items).toList(growable: false),
    );
  }

  Iterable<Widget> _sectionChildren(String title, List<Widget> items) sync* {
    yield Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Theme.of(Get.context!).colorScheme.onSurface,
      ),
    );
    yield const SizedBox(height: 12);
    yield CardOverlay(
      child: Column(
        children: _addDividers(items).toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _addDividers(List<Widget> items) sync* {
    for (int i = 0; i < items.length; i++) {
      yield items[i];
      if (i < items.length - 1) {
        yield Divider(
          height: 1,
          thickness: 1,
          color: Theme.of(Get.context!).colorScheme.outline.withOpacity(0.2),
          indent: 68, // Space for icon + padding
        );
      }
    }
  }

  Widget _buildSettingItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required Widget trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      onTap: onTap,
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: iconColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          icon,
          color: iconColor,
          size: 20,
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: trailing,
    );
  }

  Widget _buildToggleSwitch(bool value) {
    return Switch(
      value: value,
      onChanged: (newValue) {
        // Handle toggle change
      },
      activeColor: ErpColors.primary,
      inactiveThumbColor: Colors.grey[300],
      inactiveTrackColor: Colors.grey[200],
    );
  }

  Widget _buildLogoutButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          // Logout functionality
          _showLogoutDialog();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red[500],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: const Text(
          '登出帳號',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildCopyright() {
    return Column(
      children: _copyrightChildren().toList(growable: false),
    );
  }

  Iterable<Widget> _clearDataDialogActions(BuildContext ctx) sync* {
    yield TextButton(
      onPressed: () => Navigator.of(ctx).pop(false),
      child: const Text('取消'),
    );
    yield TextButton(
      onPressed: () async {
        Navigator.of(ctx).pop(true);
        // Cache messenger before async
        final messenger = ScaffoldMessenger.of(ctx);
        try {
          final msg = await controller.clearData();
          messenger.hideCurrentSnackBar();
          messenger.showSnackBar(
            SnackBar(
              content: Text(msg),
              duration: const Duration(seconds: 4),
            ),
          );
        } catch (e) {
          messenger.showSnackBar(
            SnackBar(
              content: Text('清除資料失敗：$e'),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      },
      child: const Text(
        '清除',
        style: TextStyle(color: Colors.red),
      ),
    );
  }

  Iterable<Widget> _copyrightChildren() sync* {
    yield Text(
      'PocketTrac © 2024',
      style: TextStyle(
        fontSize: 12,
        color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
      ),
    );
    yield const SizedBox(height: 4);
    yield Text(
      '服務條款 • 隱私權政策',
      style: TextStyle(
        fontSize: 12,
        color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
      ),
    );
  }

  void _showLogoutDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('確認登出'),
        content: const Text('您確定要登出帳號嗎？'),
        actions: _logoutDialogActions().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _exportDialogActions(BuildContext ctx) sync* {
    yield TextButton(
      onPressed: () => Navigator.of(ctx).pop(false),
      child: const Text('取消'),
    );
    yield TextButton(
      onPressed: () async {
        Navigator.of(ctx).pop(true);
        final messenger = ScaffoldMessenger.of(ctx);
        try {
          final msg = await controller.exportData();
          messenger.hideCurrentSnackBar();
          messenger.showSnackBar(
            SnackBar(
              content: Text(msg),
              duration: const Duration(seconds: 4),
            ),
          );
        } catch (e) {
          messenger.showSnackBar(
            SnackBar(
              content: Text('匯出失敗：$e'),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      },
      child: const Text('預設 Downloads'),
    );
    yield TextButton(
      onPressed: () async {
        Navigator.of(ctx).pop(true);
        final messenger = ScaffoldMessenger.of(ctx);
        try {
          final msg = await controller.exportData(chooseLocation: true);
          messenger.hideCurrentSnackBar();
          messenger.showSnackBar(
            SnackBar(
              content: Text(msg),
              duration: const Duration(seconds: 4),
            ),
          );
        } catch (e) {
          messenger.showSnackBar(
            SnackBar(
              content: Text('匯出失敗：$e'),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      },
      child: const Text('自選位置'),
    );
  }

  Iterable<Widget> _logoutDialogActions() sync* {
    yield TextButton(
      onPressed: () => Get.back(),
      child: const Text('取消'),
    );
    yield TextButton(
      onPressed: () {
        Get.back();
        // Perform logout
      },
      child: const Text(
        '登出',
        style: TextStyle(color: Colors.red),
      ),
    );
  }

  /// 显示语言选择对话框
  Future<void> showLanguageDialog() async {
    Get.dialog(
      AlertDialog(
        title: Text('language_selection_title'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: controller.prefProvider.supportedLocales.map((locale) {
            return Obx(() => RadioListTile(
                  title: Row(
                    children:
                        _languageRowChildren(locale).toList(growable: false),
                  ),
                  value: locale,
                  groupValue: controller.prefProvider.currentLocale,
                  onChanged: (value) {
                    if (value != null) {
                      controller.prefProvider.locale = value;
                      final messenger = ScaffoldMessenger.of(Get.context!);
                      Get.back();
                      messenger.hideCurrentSnackBar();
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text('success_language_changed'.tr),
                          duration: const Duration(seconds: 4),
                        ),
                      );
                    }
                  },
                ));
          }).toList(growable: false),
        ),
        actions: _singleCancelAction().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _languageRowChildren(dynamic locale) sync* {
    yield Text(locale.flagEmoji);
    yield const SizedBox(width: 8);
    yield Text(locale.display);
  }

  /// 显示主题选择对话框
  Future<void> showThemeDialog() async {
    Get.dialog(
      AlertDialog(
        title: Text('theme_selection_title'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ThemeMode.values.map((mode) {
            return Obx(() => RadioListTile(
                  title: Text(mode.display),
                  subtitle: Text(_getThemeModeDescription(mode)),
                  value: mode,
                  groupValue: controller.prefProvider.currentThemeMode,
                  onChanged: (value) {
                    if (value != null) {
                      controller.prefProvider.themeMode = value;
                      final messenger = ScaffoldMessenger.of(Get.context!);
                      Get.back();
                      messenger.hideCurrentSnackBar();
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text('success_theme_changed'.tr),
                          duration: const Duration(seconds: 4),
                        ),
                      );
                    }
                  },
                ));
          }).toList(growable: false),
        ),
        actions: _singleCancelAction().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _singleCancelAction() sync* {
    yield TextButton(
      onPressed: () => Get.back(),
      child: Text('cancel'.tr),
    );
  }

  /// 获取主题模式描述
  String _getThemeModeDescription(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'theme_light_desc'.tr;
      case ThemeMode.dark:
        return 'theme_dark_desc'.tr;
      case ThemeMode.system:
        return 'theme_system_desc'.tr;
    }
  }
}
