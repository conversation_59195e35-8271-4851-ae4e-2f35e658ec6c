import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:pocket_trac/app/providers/box_provider.dart';
import 'package:pocket_trac/app/providers/pref_provider.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:convert';
import 'dart:io';

import 'package:pocket_trac/app/repositories/category_repository.dart';
import 'package:pocket_trac/app/repositories/order_repository.dart';
import 'package:pocket_trac/app/models/erp_category.dart';
import 'package:pocket_trac/app/models/erp_order.dart';

class SettingsController extends GetxController with StateMixin<String> {
  final CategoryRepository categoryRepo;
  final OrderRepository orderRepo;
  final PrefProvider prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Talker get talker => boxProvider.talker;

  SettingsController({
    required this.categoryRepo,
    required this.orderRepo,
    required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
    } catch (e, s) {
      talker.error('$e', e, s);
      change('', status: RxStatus.error('Error: $e'));
    }
  }

  /// 匯出資料
  /// chooseLocation: 若為 true，將開啟資料夾選擇器自選匯出位置；否則使用預設 Downloads
  /// 成功時回傳提示訊息，錯誤時丟出例外
  Future<String> exportData({bool chooseLocation = false}) async {
    try {
      change(state, status: RxStatus.loading());
      final categories = await categoryRepo.getAllAsync(includeDeleted: true);
      final orders = await orderRepo.getAllAsync(includeDeleted: true);

      // 補強：輸出訂單時補上 parent_object_id 與備援 category_id
      final ordersPayload = orders.map((o) {
        final m = o.toJson();
        m['parent_object_id'] = o.parentObjectId ?? o.parent.target?.objectId;
        final catId = o.parent.targetId;
        if (catId != 0) {
          m['category_id'] = catId;
        }
        return m;
      }).toList();

      final payload = {
        'version': 1,
        'exportedAt': DateTime.now().toIso8601String(),
        'categories': categories.map((e) => e.toJson()).toList(),
        'orders': ordersPayload,
      };

      final dir = chooseLocation
          ? await _pickExportDir() ?? await _resolveDefaultExportDir()
          : await _resolveDefaultExportDir();
      final ts = DateTime.now();
      final fileName =
          'pockettrac-backup-${ts.year.toString().padLeft(4, '0')}${ts.month.toString().padLeft(2, '0')}${ts.day.toString().padLeft(2, '0')}-${ts.hour.toString().padLeft(2, '0')}${ts.minute.toString().padLeft(2, '0')}${ts.second.toString().padLeft(2, '0')}.json';
      final file = File('${dir.path}/$fileName');
      await file
          .writeAsString(const JsonEncoder.withIndent('  ').convert(payload));

      talker.debug('Exported backup to ${file.path}');
      change(state, status: RxStatus.success());
      return '已匯出到：${file.path}';
    } catch (e, s) {
      talker.error('Export failed: $e', e, s);
      change(state, status: RxStatus.success());
      rethrow;
    }
  }

  /// 取得預設匯出資料夾（盡可能使用 Downloads）
  Future<Directory> _resolveDefaultExportDir() async {
    try {
      if (Platform.isAndroid) {
        final dl = Directory('/storage/emulated/0/Download');
        if (await dl.exists()) return dl;
        final ext = await getExternalStorageDirectory();
        if (ext != null) return ext;
      } else if (Platform.isIOS) {
        // iOS 沒有公開 Downloads，回退到 App 文件夾
        return await getApplicationDocumentsDirectory();
      } else {
        // macOS/Windows/Linux
        final dl = await getDownloadsDirectory();
        if (dl != null) return dl;
      }
    } catch (_) {}
    // 最後回退
    return await getApplicationDocumentsDirectory();
  }

  /// 開啟資料夾選擇器，回傳選取的資料夾
  Future<Directory?> _pickExportDir() async {
    try {
      final path = await FilePicker.platform.getDirectoryPath();
      if (path == null || path.isEmpty) return null; // 取消
      final dir = Directory(path);
      if (!(await dir.exists())) {
        await dir.create(recursive: true);
      }
      return dir;
    } catch (e, s) {
      talker.error('Pick export dir failed: $e', e, s);
      return null;
    }
  }

  /// 從 JSON 檔匯入資料（透過檔案選擇器選取）
  /// 回傳成功訊息字串；若為 null 代表取消；錯誤丟出例外
  Future<String?> importData() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.custom,
        allowedExtensions: ['json'],
      );
      if (result == null || result.files.isEmpty) {
        return null; // 使用者取消
      }


      final path = result.files.single.path;
      if (path == null) {
        throw '無效的檔案路徑';
      }

      final content = await File(path).readAsString();
      // 使用背景 Isolate 解析大型 JSON，避免阻塞主執行緒
      final Map<String, dynamic> jsonData =
          await compute<_JsonDecodeArg, Map<String, dynamic>>(
              _decodeJson, _JsonDecodeArg(content));

      final List<dynamic> catList = (jsonData['categories'] as List?) ?? [];
      final List<dynamic> orderList = (jsonData['orders'] as List?) ?? [];

      // 先 upsert 類別（以 objectId 為準）
      final existingCats = await categoryRepo.getAllAsync(includeDeleted: true);
      final byObjectId = <String, ErpCategory>{
        for (final c in existingCats)
          if (c.objectId != null) c.objectId!: c
      };

      // 分批寫入類別，降低單批記憶體與交易壓力
      final catBatchSize = 5000;
      final toSaveCats = <ErpCategory>[];
      for (final item in catList) {
        final map = item as Map<String, dynamic>;
        final incoming = ErpCategory.fromJson(map);
        if (incoming.objectId != null &&
            byObjectId.containsKey(incoming.objectId!)) {
          final exist = byObjectId[incoming.objectId!]!;
          incoming.id = exist.id;
        } else {
          incoming.id = null; // 新增
        }
        toSaveCats.add(incoming);
        if (toSaveCats.length >= catBatchSize) {
          await categoryRepo.putManyAsync(toSaveCats);
          talker.debug('正在匯入分類... (${byObjectId.length + toSaveCats.length}/${catList.length})');
          toSaveCats.clear();
        }
      }
      if (toSaveCats.isNotEmpty) {
        await categoryRepo.putManyAsync(toSaveCats);
        toSaveCats.clear();
      }

      // 重新讀取類別映射
      final savedCats = await categoryRepo.getAllAsync(includeDeleted: true);
      final catByObjectId = <String, ErpCategory>{
        for (final c in savedCats)
          if (c.objectId != null) c.objectId!: c
      };
      final catById = <int, ErpCategory>{
        for (final c in savedCats)
          if (c.id != null) c.id!: c
      };

      // 再匯入訂單，並建立父關係
      // 先一次載入既有訂單 objectId -> id 對照，避免逐筆查詢
      final existingOrders = await orderRepo.getAllAsync(includeDeleted: true);
      final orderIdByObjectId = <String, int>{
        for (final o in existingOrders)
          if (o.objectId != null && o.id != null) o.objectId!: o.id!
      };

      final orderBatchSize = 5000;
      final toSaveOrders = <ErpOrder>[];
      var processedOrders = 0;
      for (final item in orderList) {
        final map = item as Map<String, dynamic>;
        final incoming = ErpOrder.fromJson(map);
        // 以 objectId 合併（如已存在則覆蓋）
        final objId = incoming.objectId;
        if (objId != null && orderIdByObjectId.containsKey(objId)) {
          incoming.id = orderIdByObjectId[objId];
        } else {
          incoming.id = null;
        }

        // 建立父關係（優先使用 parent_object_id）
        final parentObjectId = map['parent_object_id'] as String?;
        if (parentObjectId != null && catByObjectId.containsKey(parentObjectId)) {
          incoming.parent.target = catByObjectId[parentObjectId];
        } else {
          // 備援：使用 category_id 直接關聯
          final catId = (map['category_id'] as num?)?.toInt();
          if (catId != null) {
            // 若存在於本地，直接設為 target；否則至少設定 targetId
            final cat = catById[catId];
            if (cat != null) {
              incoming.parent.target = cat;
            } else {
              incoming.parent.targetId = catId;
            }
          }
        }

        toSaveOrders.add(incoming);
        processedOrders++;
        if (toSaveOrders.length >= orderBatchSize) {
          await orderRepo.putManyAsync(toSaveOrders);
          talker.debug('正在匯入帳目... ($processedOrders/${orderList.length})');
          toSaveOrders.clear();
        }
      }
      if (toSaveOrders.isNotEmpty) {
        await orderRepo.putManyAsync(toSaveOrders);
        toSaveOrders.clear();
      }

      talker.debug(
          'Imported categories=${catList.length}, orders=${orderList.length}');
      return '匯入完成（分類 ${catList.length}、帳目 ${orderList.length}）';
    } catch (e, s) {
      talker.error('Import failed: $e', e, s);
      rethrow;
    }
  }

  /// 清除所有本地資料（類別與帳目）
  /// 成功時回傳提示訊息，錯誤時丟出例外
  Future<String> clearData() async {
    try {
      change(state, status: RxStatus.loading());
      final o = await orderRepo.deleteAllAsync();
      final c = await categoryRepo.deleteAllAsync();
      talker.debug('Cleared data orders=$o, categories=$c');
      change(state, status: RxStatus.success());
      return '已清除本地資料';
    } catch (e, s) {
      talker.error('Clear data failed: $e', e, s);
      change(state, status: RxStatus.success());
      rethrow;
    }
  }
}

/// 背景 Isolate 使用的 JSON 解析參數（頂層）
class _JsonDecodeArg {
  final String content;
  const _JsonDecodeArg(this.content);
}

/// 背景 Isolate 的 JSON 解析函式（頂層）
Map<String, dynamic> _decodeJson(_JsonDecodeArg arg) {
  return json.decode(arg.content) as Map<String, dynamic>;
}
