import 'dart:async';

import 'package:objectbox/objectbox.dart';

import '../models/erp_order.dart';
import '../models/stat_daily.dart';
import '../models/stat_monthly.dart';
import '../models/category_stat_daily.dart';
import '../models/category_stat_monthly.dart';
import '../providers/box_provider.dart';
import '../../objectbox.g.dart';

/// On-read 統計維護：在讀取某一個桶（日/月）資料時，
/// 以該桶來源 ErpOrder 的 max(updatedAt) 與統計表的 updatedAt 比較，
/// 若來源較新則重算並回存統計。
class StatsRepository {
  final BoxProvider boxProvider;

  Store get store => boxProvider.store;
  Box<ErpOrder> get orderBox => store.box<ErpOrder>();
  Box<StatDaily> get statDailyBox => store.box<StatDaily>();
  Box<StatMonthly> get statMonthlyBox => store.box<StatMonthly>();
  Box<CategoryStatDaily> get catDailyBox => store.box<CategoryStatDaily>();
  Box<CategoryStatMonthly> get catMonthlyBox => store.box<CategoryStatMonthly>();

  StatsRepository(this.boxProvider);

  // ============= Public APIs =============
  Future<StatDaily> getDailyStat(DateTime date) async {
    final start = _atLocalMidnight(date);
    final end = start.add(const Duration(days: 1));
    final dayKey = _toDayKey(start);

    // 讀現有統計
    final stat = await _findStatDailyByDayKey(dayKey) ??
        StatDaily(date: start, dayKey: dayKey, createdAt: DateTime.now());

    // 是否需要重算（存在性檢查）
    final need = await _needsRecompute(stat.updatedAt, start, end);
    if (need) {
      final orders = await _getOrdersInRange(start, end);
      final agg = _aggregateOrders(orders);
      stat
        ..expenseCents = agg.expense
        ..incomeCents = agg.income
        ..ignoreCents = agg.ignore
        ..updatedAt = DateTime.now();
      statDailyBox.put(stat);
    }

    return stat;
  }

  Future<StatMonthly> getMonthlyStat(DateTime anyDayInMonth) async {
    final start = _atLocalMonthStart(anyDayInMonth);
    final end = _nextMonthStart(start);
    final monthKey = _toMonthKey(start);

    final stat = await _findStatMonthlyByMonthKey(monthKey) ??
        StatMonthly(month: start, monthKey: monthKey, createdAt: DateTime.now());

    // 是否需要重算（存在性檢查）
    final need = await _needsRecompute(stat.updatedAt, start, end);
    if (need) {
      final orders = await _getOrdersInRange(start, end);
      final agg = _aggregateOrders(orders);
      stat
        ..expenseCents = agg.expense
        ..incomeCents = agg.income
        ..ignoreCents = agg.ignore
        ..updatedAt = DateTime.now();
      statMonthlyBox.put(stat);
    }

    return stat;
  }

  Future<CategoryStatDaily> getDailyCategoryStat(DateTime date, int categoryId) async {
    final start = _atLocalMidnight(date);
    final end = start.add(const Duration(days: 1));
    final dayKey = _toDayKey(start);

    final stat = await _findCatDaily(dayKey, categoryId) ?? CategoryStatDaily(
      date: start,
      dayKey: dayKey,
      categoryId: categoryId,
      createdAt: DateTime.now(),
    );

    // 是否需要重算（存在性檢查）
    final need = await _needsRecompute(stat.updatedAt, start, end, categoryId: categoryId);
    if (need) {
      final orders = await _getOrdersInRange(start, end, categoryId: categoryId);
      final agg = _aggregateOrders(orders, includeIgnore: false);
      stat
        ..expenseCents = agg.expense
        ..incomeCents = agg.income
        ..updatedAt = DateTime.now();
      catDailyBox.put(stat);
    }

    return stat;
  }

  Future<CategoryStatMonthly> getMonthlyCategoryStat(DateTime anyDayInMonth, int categoryId) async {
    final start = _atLocalMonthStart(anyDayInMonth);
    final end = _nextMonthStart(start);
    final monthKey = _toMonthKey(start);

    final stat = await _findCatMonthly(monthKey, categoryId) ?? CategoryStatMonthly(
      month: start,
      monthKey: monthKey,
      categoryId: categoryId,
      createdAt: DateTime.now(),
    );

    // 是否需要重算（存在性檢查）
    final need = await _needsRecompute(stat.updatedAt, start, end, categoryId: categoryId);
    if (need) {
      final orders = await _getOrdersInRange(start, end, categoryId: categoryId);
      final agg = _aggregateOrders(orders, includeIgnore: false);
      stat
        ..expenseCents = agg.expense
        ..incomeCents = agg.income
        ..updatedAt = DateTime.now();
      catMonthlyBox.put(stat);
    }

    return stat;
  }

  // ============= Private: find existing stats =============
  Future<StatDaily?> _findStatDailyByDayKey(int dayKey) async {
    final q = statDailyBox.query(StatDaily_.dayKey.equals(dayKey)).build();
    try {
      return await q.findFirstAsync();
    } finally {
      q.close();
    }
  }

  Future<StatMonthly?> _findStatMonthlyByMonthKey(int monthKey) async {
    final q = statMonthlyBox.query(StatMonthly_.monthKey.equals(monthKey)).build();
    try {
      return await q.findFirstAsync();
    } finally {
      q.close();
    }
  }

  Future<CategoryStatDaily?> _findCatDaily(int dayKey, int categoryId) async {
    final q = catDailyBox
        .query(CategoryStatDaily_.dayKey.equals(dayKey).and(CategoryStatDaily_.categoryId.equals(categoryId)))
        .build();
    try {
      return await q.findFirstAsync();
    } finally {
      q.close();
    }
  }

  Future<CategoryStatMonthly?> _findCatMonthly(int monthKey, int categoryId) async {
    final q = catMonthlyBox
        .query(CategoryStatMonthly_.monthKey.equals(monthKey).and(CategoryStatMonthly_.categoryId.equals(categoryId)))
        .build();
    try {
      return await q.findFirstAsync();
    } finally {
      q.close();
    }
  }

  // ============= Private: order queries & aggregation =============
  Future<bool> _needsRecompute(DateTime? statUpdatedAt, DateTime start, DateTime end, {int? categoryId}) async {
    // 第一次讀：無論是否有來源資料都建立（保持與原邏輯一致）
    if (statUpdatedAt == null) return true;

    // 存在性檢查：是否有任何比桶新的來源訂單
    final base = _buildBaseOrderRangeCond(start, end, categoryId: categoryId);
    final cond = base.and(ErpOrder_.updatedAt.greaterThan(statUpdatedAt.millisecondsSinceEpoch));
    final q = orderBox.query(cond).build();
    try {
      q.limit = 1;
      final first = await q.findFirstAsync();
      return first != null;
    } finally {
      q.close();
    }
  }
  

  Future<List<ErpOrder>> _getOrdersInRange(DateTime start, DateTime end, {int? categoryId}) async {
    final cond = _buildBaseOrderRangeCond(start, end, categoryId: categoryId);
    final q = orderBox.query(cond).build();
    try {
      return await q.findAsync();
    } finally {
      q.close();
    }
  }

  Condition<ErpOrder> _buildBaseOrderRangeCond(DateTime start, DateTime end, {int? categoryId}) {
    Condition<ErpOrder> cond = ErpOrder_.deletedAt.isNull()
        .and(ErpOrder_.triggerAt.greaterOrEqual(start.millisecondsSinceEpoch))
        .and(ErpOrder_.triggerAt.lessThan(end.millisecondsSinceEpoch));
    if (categoryId != null) {
      cond = cond.and(ErpOrder_.parent.equals(categoryId));
    }
    return cond;
  }

  _Agg _aggregateOrders(List<ErpOrder> orders, {bool includeIgnore = true}) {
    int expense = 0;
    int income = 0;
    int ignore = 0;
    for (final o in orders) {
      final t = o.type;
      final cents = o.amountCents ?? 0;
      if (t == null) continue;
      if (t == 0) { // TransactionType.expense
        expense += cents;
      } else if (t == 1) { // TransactionType.income
        income += cents;
      } else if (includeIgnore && t == 2) { // TransactionType.ignore
        ignore += cents;
      }
    }
    return _Agg(expense: expense, income: income, ignore: ignore);
  }

  

  // ============= Private: time & key helpers =============
  DateTime _atLocalMidnight(DateTime d) => DateTime(d.year, d.month, d.day);

  DateTime _atLocalMonthStart(DateTime d) => DateTime(d.year, d.month, 1);

  DateTime _nextMonthStart(DateTime monthStart) =>
      DateTime(monthStart.year, monthStart.month + 1, 1);

  int _toDayKey(DateTime d) => d.year * 10000 + d.month * 100 + d.day;

  int _toMonthKey(DateTime d) => d.year * 100 + d.month;
}

class _Agg {
  final int expense;
  final int income;
  final int ignore;
  _Agg({required this.expense, required this.income, required this.ignore});
}
