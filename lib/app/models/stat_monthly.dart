import 'dart:convert';

import 'package:objectbox/objectbox.dart';

/// 每月總覽統計（不分分類）
@Entity()
class StatMonthly {
  @Id()
  int? id;

  @Property(type: PropertyType.date)
  DateTime? createdAt;

  @Property(type: PropertyType.date)
  DateTime? updatedAt;

  /// 該月的第一天（以 00:00:00 本地時間對齊）
  @Property(type: PropertyType.date)
  @Index()
  DateTime month;

  /// 本地分桶鍵（yyyymm），用於穩定月維度查詢
  @Index()
  int monthKey;

  /// 金額（分）
  int expenseCents;
  int incomeCents;
  int ignoreCents;

  StatMonthly({
    this.id,
    this.createdAt,
    this.updatedAt,
    required this.month,
    required this.monthKey,
    this.expenseCents = 0,
    this.incomeCents = 0,
    this.ignoreCents = 0,
  });

  factory StatMonthly.fromRawJson(String str) =>
      StatMonthly.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StatMonthly.fromJson(Map<String, dynamic> json) => StatMonthly(
        id: json['id'],
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null
            ? null
            : DateTime.parse(json['updated_at']),
        month: DateTime.parse(json['month']),
        monthKey: json['month_key'],
        expenseCents: json['expense_cents'] ?? 0,
        incomeCents: json['income_cents'] ?? 0,
        ignoreCents: json['ignore_cents'] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'month': month.toIso8601String(),
        'month_key': monthKey,
        'expense_cents': expenseCents,
        'income_cents': incomeCents,
        'ignore_cents': ignoreCents,
      };
}
