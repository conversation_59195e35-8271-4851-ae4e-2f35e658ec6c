import 'dart:convert';

import 'package:objectbox/objectbox.dart';

import 'erp_category.dart';

@Entity()
class ErpOrder {
  @Id()
  int? id;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  DateTime? createdAt;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  @Index()
  DateTime? updatedAt;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  @Index()
  DateTime? deletedAt;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  @Index()
  DateTime? triggerAt;
  @Index()
  int? type;
  @Index()
  int? amountCents;
  String? note;
  double? latitude;
  double? longitude;
  String? parentTable;
  String? parentObjectId;
  @Index()
  String? objectId;
  final parent = ToOne<ErpCategory>();

  ErpOrder({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.triggerAt,
    this.latitude,
    this.longitude,
    this.type,
    this.amountCents,
    this.note,
    this.parentTable,
    this.parentObjectId,
    this.objectId,
  });

  ErpOrder copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    DateTime? triggerAt,
    double? latitude,
    double? longitude,
    int? type,
    int? amountCents,
    String? note,
    String? parentObjectId,
    String? parentTable,
    String? objectId,
  }) =>
      ErpOrder(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        triggerAt: triggerAt ?? this.triggerAt,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        type: type ?? this.type,
        amountCents: amountCents ?? this.amountCents,
        note: note ?? this.note,
        parentTable: parentTable ?? this.parentTable,
        parentObjectId: parentObjectId ?? this.parentObjectId,
        objectId: objectId ?? this.objectId,
      );

  factory ErpOrder.fromRawJson(String str) =>
      ErpOrder.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ErpOrder.fromJson(Map<String, dynamic> json) => ErpOrder(
        id: json["id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"] == null
            ? null
            : DateTime.parse(json["deleted_at"]),
        triggerAt: json["trigger_at"] == null
            ? null
            : DateTime.parse(json["trigger_at"]),
        type: json["type"],
        amountCents: json["amount_cents"],
        note: json["note"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        parentTable: json["parent_table"],
        parentObjectId: json["parent_object_id"],
        objectId: json["object_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt?.toIso8601String(),
        "trigger_at": triggerAt?.toIso8601String(),
        "type": type,
        "amount_cents": amountCents,
        "note": note,
        "latitude": latitude,
        "longitude": longitude,
        "parent_table": parentTable,
        "parent_object_id": parentObjectId,
        "object_id": objectId,
      };
}
