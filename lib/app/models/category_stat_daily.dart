import 'dart:convert';

import 'package:objectbox/objectbox.dart';

/// 每日 x 分類 統計
@Entity()
class CategoryStatDaily {
  @Id()
  int? id;

  @Property(type: PropertyType.date)
  DateTime? createdAt;

  @Property(type: PropertyType.date)
  DateTime? updatedAt;

  /// 該天的日期（以 00:00:00 本地時間對齊），只取日
  @Property(type: PropertyType.date)
  @Index()
  DateTime date;

  /// 本地分桶鍵（yyyymmdd），用於穩定日維度查詢
  @Index()
  int dayKey;

  /// 分類 ID（對應 ErpCategory.id）
  @Index()
  int categoryId;

  /// 金額（分）
  int expenseCents;
  int incomeCents;

  CategoryStatDaily({
    this.id,
    this.createdAt,
    this.updatedAt,
    required this.date,
    required this.dayKey,
    required this.categoryId,
    this.expenseCents = 0,
    this.incomeCents = 0,
  });

  factory CategoryStatDaily.fromRawJson(String str) =>
      CategoryStatDaily.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CategoryStatDaily.fromJson(Map<String, dynamic> json) => CategoryStatDaily(
        id: json['id'],
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null
            ? null
            : DateTime.parse(json['updated_at']),
        date: DateTime.parse(json['date']),
        dayKey: json['day_key'],
        categoryId: json['category_id'],
        expenseCents: json['expense_cents'] ?? 0,
        incomeCents: json['income_cents'] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'date': date.toIso8601String(),
        'day_key': dayKey,
        'category_id': categoryId,
        'expense_cents': expenseCents,
        'income_cents': incomeCents,
      };
}
