import 'dart:convert';

import 'package:objectbox/objectbox.dart';

/// 每月 x 分類 統計
@Entity()
class CategoryStatMonthly {
  @Id()
  int? id;

  @Property(type: PropertyType.date)
  DateTime? createdAt;

  @Property(type: PropertyType.date)
  DateTime? updatedAt;

  /// 該月的第一天（以 00:00:00 本地時間對齊）
  @Property(type: PropertyType.date)
  @Index()
  DateTime month;

  /// 本地分桶鍵（yyyymm），用於穩定月維度查詢
  @Index()
  int monthKey;

  /// 分類 ID（對應 ErpCategory.id）
  @Index()
  int categoryId;

  /// 金額（分）
  int expenseCents;
  int incomeCents;

  CategoryStatMonthly({
    this.id,
    this.createdAt,
    this.updatedAt,
    required this.month,
    required this.monthKey,
    required this.categoryId,
    this.expenseCents = 0,
    this.incomeCents = 0,
  });

  factory CategoryStatMonthly.fromRawJson(String str) =>
      CategoryStatMonthly.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CategoryStatMonthly.fromJson(Map<String, dynamic> json) => CategoryStatMonthly(
        id: json['id'],
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null
            ? null
            : DateTime.parse(json['updated_at']),
        month: DateTime.parse(json['month']),
        monthKey: json['month_key'],
        categoryId: json['category_id'],
        expenseCents: json['expense_cents'] ?? 0,
        incomeCents: json['income_cents'] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'month': month.toIso8601String(),
        'month_key': monthKey,
        'category_id': categoryId,
        'expense_cents': expenseCents,
        'income_cents': incomeCents,
      };
}
