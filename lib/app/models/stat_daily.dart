import 'dart:convert';

import 'package:objectbox/objectbox.dart';

/// 每日總覽統計（不分分類）
@Entity()
class StatDaily {
  @Id()
  int? id;

  @Property(type: PropertyType.date)
  DateTime? createdAt;

  @Property(type: PropertyType.date)
  DateTime? updatedAt;

  /// 該天的日期（以 00:00:00 本地時間對齊），只取日
  @Property(type: PropertyType.date)
  @Index()
  DateTime date;

  /// 本地分桶鍵（yyyymmdd），用於穩定日維度查詢
  @Index()
  int dayKey;

  /// 金額（分）
  int expenseCents;
  int incomeCents;
  int ignoreCents;

  StatDaily({
    this.id,
    this.createdAt,
    this.updatedAt,
    required this.date,
    required this.dayKey,
    this.expenseCents = 0,
    this.incomeCents = 0,
    this.ignoreCents = 0,
  });

  factory StatDaily.fromRawJson(String str) =>
      StatDaily.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StatDaily.fromJson(Map<String, dynamic> json) => StatDaily(
        id: json['id'],
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null
            ? null
            : DateTime.parse(json['updated_at']),
        date: DateTime.parse(json['date']),
        dayKey: json['day_key'],
        expenseCents: json['expense_cents'] ?? 0,
        incomeCents: json['income_cents'] ?? 0,
        ignoreCents: json['ignore_cents'] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'date': date.toIso8601String(),
        'day_key': dayKey,
        'expense_cents': expenseCents,
        'income_cents': incomeCents,
        'ignore_cents': ignoreCents,
      };
}
