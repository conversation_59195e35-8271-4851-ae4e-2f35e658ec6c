import 'package:flutter/material.dart';

import '../../colors.dart';
import '../models/erp_category.dart';
import 'category_grid_item.dart';

class CategoryGridView extends StatelessWidget {
  final List<ErpCategory> categories;
  final ErpCategory? selectedCategory;
  final ValueChanged<ErpCategory>? onCategorySelected;
  final bool showAddButton;
  final VoidCallback? onAddCategory;

  const CategoryGridView({
    super.key,
    required this.categories,
    this.selectedCategory,
    this.onCategorySelected,
    this.showAddButton = false,
    this.onAddCategory,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (categories.isEmpty) {
      if (showAddButton) {
        return GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.0,
          ),
          itemCount: 1,
          itemBuilder: (context, index) {
            return _AddCategoryGridItem(
              onTap: onAddCategory,
              isDark: isDark,
            );
          },
        );
      }
      return Center(
        child: Text(
          '暫無分類',
          style: TextStyle(
            color:
                isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
          ),
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: categories.length + (showAddButton ? 1 : 0),
      itemBuilder: (context, index) {
        if (showAddButton && index == 0) {
          // 新增分類卡片
          return _AddCategoryGridItem(
            onTap: onAddCategory,
            isDark: isDark,
          );
        }

        final realIndex = showAddButton ? index - 1 : index;
        final category = categories[realIndex];
        return CategoryGridItem(
          category,
          isSelected: category == selectedCategory,
          onTap: () => onCategorySelected?.call(category),
        );
      },
    );
  }
}

class _AddCategoryGridItem extends StatelessWidget {
  final VoidCallback? onTap;
  final bool isDark;

  const _AddCategoryGridItem({
    required this.onTap,
    required this.isDark,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkSurface : const Color(0xFFF9FAFB),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: _addCategoryChildren(isDark).toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _addCategoryChildren(bool isDark) sync* {
    yield Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: (isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary)
            .withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        Icons.add,
        size: 20,
        color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
      ),
    );
    yield const SizedBox(height: 8);
    yield Text(
      '新增分類',
      textAlign: TextAlign.center,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
      ),
    );
  }
}
