import 'package:flutter/material.dart';

import '../../colors.dart';
import '../../enums.dart';

class TransactionTypeSelector extends StatelessWidget {
  final TransactionType transactionType;
  final ValueChanged<TransactionType>? onChanged;

  const TransactionTypeSelector({
    super.key,
    required this.transactionType,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return Container(
      decoration: BoxDecoration(
        color: isDark ? ErpColors.darkSurface : const Color(0xFFF3F4F6),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(4),
      child: Row(
        children: _buildBodyChildren(isDark).toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _buildBodyChildren(bool isDark) sync* {
    for (final type in TransactionType.values) {
      final isSelected = transactionType == type;
      yield Expanded(
        child: GestureDetector(
          onTap: () => onChanged?.call(type),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? (isDark ? ErpColors.darkCardBackground : Colors.white)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(6),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ]
                  : null,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _optionRowChildren(type, isSelected, isDark)
                  .toList(growable: false),
            ),
          ),
        ),
      );
    }
  }

  Iterable<Widget> _optionRowChildren(
      TransactionType type, bool isSelected, bool isDark) sync* {
    yield Icon(
      type.icon,
      size: 16,
      color: isSelected
          ? ErpColors.primary
          : (isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary),
    );
    yield const SizedBox(width: 4);
    yield Text(
      type.displayName,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: isSelected
            ? ErpColors.primary
            : (isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary),
      ),
    );
  }
}
