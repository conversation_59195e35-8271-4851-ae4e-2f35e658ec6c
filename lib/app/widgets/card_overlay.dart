import 'package:flutter/material.dart';

class CardOverlay extends StatelessWidget {
  final double alpha;
  final double radius;
  final Widget child;
  final EdgeInsetsGeometry padding;

  const CardOverlay({
    super.key,
    required this.child,
    this.alpha = 0.3,
    this.radius = 12,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      color: theme.cardColor.withValues(alpha: alpha),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radius),
        side: BorderSide(
          color: Colors.white.withValues(alpha: 0.2),
        ),
      ),
      child: Padding(
        padding: padding,
        child: child,
      ),
    );
  }
}
