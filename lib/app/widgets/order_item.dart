import 'package:flutter/material.dart';
import 'package:pocket_trac/extension.dart';
import 'package:pocket_trac/enums.dart';

import '../models/erp_order.dart';

class OrderItem extends StatelessWidget {
  final ErpOrder order;
  final VoidCallback? onTap;

  const OrderItem({
    super.key,
    required this.order,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      tileColor: context.cardOverlay(),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      onTap: onTap,
      leading: _buildTransactionIcon(context),
      title: _buildTitle(context),
      subtitle: _buildSubtitle(context),
      trailing: _buildTrailing(context),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: context.cardOverlayBorder(),
          width: 1,
        ),
      ),
    );
  }

  Widget _buildTransactionIcon(BuildContext context) {
    final category = order.parent.target;
    final theme = Theme.of(context);

    // Get category icon and color, with fallbacks
    final iconData = category?.getIcon() ?? Icons.receipt;
    final iconColor = category?.getColor() ?? theme.colorScheme.primary;
    final backgroundColor = iconColor.withValues(alpha: 0.1);

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 24,
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    final theme = Theme.of(context);
    final type = order.transactionType;
    late final Color? color;
    late final String text;

    switch (type) {
      case TransactionType.income:
        color = theme.colorScheme.secondary;
        text = '+${order.formattedAmount}';
        break;
      case TransactionType.expense:
        color = theme.colorScheme.error;
        text = '-${order.formattedAmount}';
        break;
      case TransactionType.ignore:
        color = theme.textTheme.bodyMedium?.color;
        text = order.formattedAmount; // 不計，不顯示正負號
        break;
    }

    return Text(
      text,
      style: theme.textTheme.titleMedium?.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: color,
          ) ??
          TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: color,
          ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    if (order.note == null || order.note!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Text(
      order.note!,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildTrailing(BuildContext context) {
    final theme = Theme.of(context);
    // 當 triggerAt 為 null 時不顯示
    final triggerAt = order.triggerAt;
    if (triggerAt == null) return const SizedBox.shrink();

    return Text(
      triggerAt.formattedTime,
      style: theme.textTheme.bodySmall?.copyWith(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: theme.textTheme.bodyMedium?.color,
          ) ??
          const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
    );
  }
}
