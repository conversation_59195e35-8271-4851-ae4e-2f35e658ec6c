import 'package:flutter/widgets.dart';

class Background extends StatelessWidget {
  final Widget background;
  final Widget child;
  const Background({
    super.key,
    required this.background,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield background;
    yield child;
  }
}
