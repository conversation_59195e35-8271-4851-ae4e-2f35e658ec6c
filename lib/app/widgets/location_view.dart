import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../../colors.dart';

class LocationView extends StatelessWidget {
  final VoidCallback? onTap;
  final LatLng location;

  const LocationView({
    super.key,
    this.onTap,
    required this.location,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 120,
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          border: Border.all(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: _buildBodyChildren(isDark).toList(growable: false),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _buildBodyChildren(bool isDark) sync* {
    // 地圖
    yield _buildFlutterMap();
    // 覆蓋層，顯示地點資訊
    yield _buildLocationInfoCard();
  }

  Widget _buildLocationInfoCard() {
    Iterable<Widget> getChildren() sync* {
      yield const Icon(
        Icons.location_on,
        color: Colors.white,
        size: 16,
      );
      yield const SizedBox(width: 4);
      yield Expanded(
        child: Text(
          '${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}',
          maxLines: 1,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      );
      yield const Icon(
        Icons.edit,
        color: Colors.white,
        size: 16,
      );
    }

    return Positioned(
      bottom: 8,
      left: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          children: getChildren().toList(growable: false),
        ),
      ),
    );
  }

  Widget _buildFlutterMap() {
    Iterable<Widget> getChildren() sync* {
      yield TileLayer(
        urlTemplate:
            'https://wmts.nlsc.gov.tw/wmts/EMAP/default/GoogleMapsCompatible/{z}/{y}/{x}',
      );
      yield MarkerLayer(
        markers: [
          Marker(
            point: location,
            width: 40,
            height: 40,
            child: const Icon(
              Icons.location_on,
              color: Colors.red,
              size: 30,
            ),
          ),
        ],
      );
    }

    return FlutterMap(
      key: ValueKey('${location.latitude},${location.longitude}'),
      options: MapOptions(
        initialCenter: location,
        initialZoom: 15.0,
        interactionOptions: const InteractionOptions(
          flags: InteractiveFlag.none, // 禁用所有交互
        ),
      ),
      children: getChildren().toList(growable: false),
    );
  }
}
