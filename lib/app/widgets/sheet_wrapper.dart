import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../colors.dart';

class SheetWrapper extends StatelessWidget {
  final Widget child;
  final VoidCallback? onInit;
  final String title;
  final bool showHeader;

  const SheetWrapper({
    super.key,
    required this.child,
    this.onInit,
    this.title = '',
    this.showHeader = true,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 執行初始化回調
        onInit?.call();
        return DraggableScrollableSheet(
          initialChildSize: 0.9, // 初始高度為屏幕的90%
          minChildSize: 0.5, // 最小高度為屏幕的50%
          maxChildSize: 0.9, // 最大高度為屏幕的90%
          expand: false, // 不要讓子元素擴展到整個可滾動區域
          builder: (context, scrollController) {
            return _buildBody(context);
          },
        );
      },
    );
  }

  Widget _buildBody(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return DecoratedBox(
      decoration: BoxDecoration(
        color: isDark ? ErpColors.darkCardBackground : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: getChildren(context).toList(growable: false),
      ),
    );
  }

  Iterable<Widget> getChildren(BuildContext context) sync* {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // 拖拽指示器
    yield Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      height: 4,
      width: 40,
      decoration: BoxDecoration(
        color: isDark ? ErpColors.darkTextSecondary : Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );

    // 標題欄（根據 showHeader 變數決定是否顯示）
    if (showHeader) {
      yield Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
          ),
        ),
        child: _buildHeader(context),
      );
    }

    // 內容區域
    yield Flexible(child: child);
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final isDark = theme.brightness == Brightness.dark;

    return Row(
      children: _headerRowChildren(textTheme, isDark).toList(growable: false),
    );
  }

  Iterable<Widget> _headerRowChildren(TextTheme textTheme, bool isDark) sync* {
    yield Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: textTheme.bodyLarge?.color ??
            (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
      ),
    );
    yield const Spacer();
    yield IconButton(
      onPressed: () => Get.back(),
      icon: Icon(
        Icons.close,
        color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
      ),
      style: IconButton.styleFrom(
        padding: EdgeInsets.zero,
        minimumSize: const Size(24, 24),
      ),
    );
  }
}
