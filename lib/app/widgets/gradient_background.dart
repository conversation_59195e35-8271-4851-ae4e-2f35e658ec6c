import 'package:flutter/material.dart';

class GradientBackground extends StatelessWidget {
  final Widget child;
  const GradientBackground({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bg = theme.cardColor;
    final primary = theme.colorScheme.primary;
    final secondary = theme.colorScheme.secondary;
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            bg.withValues(alpha: 0.9),
            primary.withValues(alpha: 0.6),
            secondary.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: child,
    );
  }
}
