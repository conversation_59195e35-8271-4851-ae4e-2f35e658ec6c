// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'app/models/category_stat_daily.dart';
import 'app/models/category_stat_monthly.dart';
import 'app/models/erp_category.dart';
import 'app/models/erp_order.dart';
import 'app/models/stat_daily.dart';
import 'app/models/stat_monthly.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 4263568156448590138),
      name: 'ErpCategory',
      lastPropertyId: const obx_int.IdUid(8, 5537962920687809235),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 6973479604480218883),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 5404992646398946178),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 5399158483566441334),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 4238962758334792600),
            name: 'deletedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 2534617005488803954),
            name: 'icon',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 1939368855113477500),
            name: 'name',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 207035786707513344),
            name: 'color',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 5537962920687809235),
            name: 'objectId',
            type: 9,
            flags: 2048,
            indexId: const obx_int.IdUid(1, 7457646028801894804))
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[
        obx_int.ModelBacklink(
            name: 'children', srcEntity: 'ErpOrder', srcField: '')
      ]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(2, 2234295978937104718),
      name: 'ErpOrder',
      lastPropertyId: const obx_int.IdUid(14, 4816136684987338547),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 4482650744788033352),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 7571006734245772710),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 9161864440581745813),
            name: 'updatedAt',
            type: 10,
            flags: 8,
            indexId: const obx_int.IdUid(14, 1302029547348180523)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 9084615470992907779),
            name: 'deletedAt',
            type: 10,
            flags: 8,
            indexId: const obx_int.IdUid(2, 1913379964610372421)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 5365393893501017416),
            name: 'triggerAt',
            type: 10,
            flags: 8,
            indexId: const obx_int.IdUid(3, 1588528539335536957)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 2454432426408749132),
            name: 'type',
            type: 6,
            flags: 8,
            indexId: const obx_int.IdUid(4, 5060732149003602289)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 2143104699447349483),
            name: 'amountCents',
            type: 6,
            flags: 8,
            indexId: const obx_int.IdUid(5, 2350803106828121368)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 3069870610088397394),
            name: 'note',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 2774582886422518406),
            name: 'latitude',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 5729695884488303313),
            name: 'longitude',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(11, 5853075395923010871),
            name: 'parentTable',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(12, 9109953502115605704),
            name: 'parentObjectId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(13, 1294929516121474558),
            name: 'objectId',
            type: 9,
            flags: 2048,
            indexId: const obx_int.IdUid(6, 2498094215395655168)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(14, 4816136684987338547),
            name: 'parentId',
            type: 11,
            flags: 520,
            indexId: const obx_int.IdUid(7, 7349313977645257979),
            relationTarget: 'ErpCategory')
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(3, 4374428288072666521),
      name: 'CategoryStatDaily',
      lastPropertyId: const obx_int.IdUid(8, 5190637548319637642),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 3667100828735073108),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 2694488576948452548),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 7072942653253404915),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 396461395278682162),
            name: 'date',
            type: 10,
            flags: 8,
            indexId: const obx_int.IdUid(8, 6666251678233067954)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 2313046827927329670),
            name: 'dayKey',
            type: 6,
            flags: 8,
            indexId: const obx_int.IdUid(9, 1850950435358168263)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 2203490233511675422),
            name: 'categoryId',
            type: 6,
            flags: 8,
            indexId: const obx_int.IdUid(10, 9044768589595402072)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 5573629200217703012),
            name: 'expenseCents',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 5190637548319637642),
            name: 'incomeCents',
            type: 6,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(4, 3316324142893745846),
      name: 'CategoryStatMonthly',
      lastPropertyId: const obx_int.IdUid(8, 8591071034375521991),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 3658751400209171635),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 8425189184054497314),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 4873300501343625118),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 356637010757728382),
            name: 'month',
            type: 10,
            flags: 8,
            indexId: const obx_int.IdUid(11, 5740854185337933322)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 2338923525290612273),
            name: 'monthKey',
            type: 6,
            flags: 8,
            indexId: const obx_int.IdUid(12, 3823304109925032451)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 1365162104537771681),
            name: 'categoryId',
            type: 6,
            flags: 8,
            indexId: const obx_int.IdUid(13, 8028577102462179978)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 1819742442875586756),
            name: 'expenseCents',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 8591071034375521991),
            name: 'incomeCents',
            type: 6,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(5, 5938509676235791437),
      name: 'StatDaily',
      lastPropertyId: const obx_int.IdUid(8, 8555068376416211915),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 7822672508038234223),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 7191297150487217701),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 881849606837725701),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 4663142481420368812),
            name: 'date',
            type: 10,
            flags: 8,
            indexId: const obx_int.IdUid(15, 7439379234041283820)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 6354485901337829974),
            name: 'dayKey',
            type: 6,
            flags: 8,
            indexId: const obx_int.IdUid(16, 8343634668327262700)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 6157544363100247407),
            name: 'expenseCents',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 1629144580827587811),
            name: 'incomeCents',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 8555068376416211915),
            name: 'ignoreCents',
            type: 6,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(6, 9088891593228698439),
      name: 'StatMonthly',
      lastPropertyId: const obx_int.IdUid(8, 9083178185490747247),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 3142140856383074446),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 7480107017826969314),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 7884221065602010206),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 4341434550391848191),
            name: 'month',
            type: 10,
            flags: 8,
            indexId: const obx_int.IdUid(17, 8734294621085267654)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 2005507321121540937),
            name: 'monthKey',
            type: 6,
            flags: 8,
            indexId: const obx_int.IdUid(18, 2566137204271118520)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 4607754078383479522),
            name: 'expenseCents',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 5931206802816103082),
            name: 'incomeCents',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 9083178185490747247),
            name: 'ignoreCents',
            type: 6,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(6, 9088891593228698439),
      lastIndexId: const obx_int.IdUid(18, 2566137204271118520),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [],
      retiredPropertyUids: const [],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    ErpCategory: obx_int.EntityDefinition<ErpCategory>(
        model: _entities[0],
        toOneRelations: (ErpCategory object) => [],
        toManyRelations: (ErpCategory object) => {
              obx_int.RelInfo<ErpOrder>.toOneBacklink(
                      14, object.id!, (ErpOrder srcObject) => srcObject.parent):
                  object.children
            },
        getId: (ErpCategory object) => object.id,
        setId: (ErpCategory object, int id) {
          object.id = id;
        },
        objectToFB: (ErpCategory object, fb.Builder fbb) {
          final iconOffset =
              object.icon == null ? null : fbb.writeString(object.icon!);
          final nameOffset =
              object.name == null ? null : fbb.writeString(object.name!);
          final colorOffset =
              object.color == null ? null : fbb.writeString(object.color!);
          final objectIdOffset = object.objectId == null
              ? null
              : fbb.writeString(object.objectId!);
          fbb.startTable(9);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.deletedAt?.millisecondsSinceEpoch);
          fbb.addOffset(4, iconOffset);
          fbb.addOffset(5, nameOffset);
          fbb.addOffset(6, colorOffset);
          fbb.addOffset(7, objectIdOffset);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final deletedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 10);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final deletedAtParam = deletedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(deletedAtValue);
          final iconParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 12);
          final nameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 14);
          final objectIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 18);
          final colorParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 16);
          final object = ErpCategory(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              deletedAt: deletedAtParam,
              icon: iconParam,
              name: nameParam,
              objectId: objectIdParam,
              color: colorParam);
          obx_int.InternalToManyAccess.setRelInfo<ErpCategory>(
              object.children,
              store,
              obx_int.RelInfo<ErpOrder>.toOneBacklink(
                  14, object.id!, (ErpOrder srcObject) => srcObject.parent));
          return object;
        }),
    ErpOrder: obx_int.EntityDefinition<ErpOrder>(
        model: _entities[1],
        toOneRelations: (ErpOrder object) => [object.parent],
        toManyRelations: (ErpOrder object) => {},
        getId: (ErpOrder object) => object.id,
        setId: (ErpOrder object, int id) {
          object.id = id;
        },
        objectToFB: (ErpOrder object, fb.Builder fbb) {
          final noteOffset =
              object.note == null ? null : fbb.writeString(object.note!);
          final parentTableOffset = object.parentTable == null
              ? null
              : fbb.writeString(object.parentTable!);
          final parentObjectIdOffset = object.parentObjectId == null
              ? null
              : fbb.writeString(object.parentObjectId!);
          final objectIdOffset = object.objectId == null
              ? null
              : fbb.writeString(object.objectId!);
          fbb.startTable(15);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.deletedAt?.millisecondsSinceEpoch);
          fbb.addInt64(4, object.triggerAt?.millisecondsSinceEpoch);
          fbb.addInt64(5, object.type);
          fbb.addInt64(6, object.amountCents);
          fbb.addOffset(7, noteOffset);
          fbb.addFloat64(8, object.latitude);
          fbb.addFloat64(9, object.longitude);
          fbb.addOffset(10, parentTableOffset);
          fbb.addOffset(11, parentObjectIdOffset);
          fbb.addOffset(12, objectIdOffset);
          fbb.addInt64(13, object.parent.targetId);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final deletedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 10);
          final triggerAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 12);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final deletedAtParam = deletedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(deletedAtValue);
          final triggerAtParam = triggerAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(triggerAtValue);
          final latitudeParam = const fb.Float64Reader()
              .vTableGetNullable(buffer, rootOffset, 20);
          final longitudeParam = const fb.Float64Reader()
              .vTableGetNullable(buffer, rootOffset, 22);
          final typeParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 14);
          final amountCentsParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 16);
          final noteParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 18);
          final parentTableParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 24);
          final parentObjectIdParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 26);
          final objectIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 28);
          final object = ErpOrder(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              deletedAt: deletedAtParam,
              triggerAt: triggerAtParam,
              latitude: latitudeParam,
              longitude: longitudeParam,
              type: typeParam,
              amountCents: amountCentsParam,
              note: noteParam,
              parentTable: parentTableParam,
              parentObjectId: parentObjectIdParam,
              objectId: objectIdParam);
          object.parent.targetId =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 30, 0);
          object.parent.attach(store);
          return object;
        }),
    CategoryStatDaily: obx_int.EntityDefinition<CategoryStatDaily>(
        model: _entities[2],
        toOneRelations: (CategoryStatDaily object) => [],
        toManyRelations: (CategoryStatDaily object) => {},
        getId: (CategoryStatDaily object) => object.id,
        setId: (CategoryStatDaily object, int id) {
          object.id = id;
        },
        objectToFB: (CategoryStatDaily object, fb.Builder fbb) {
          fbb.startTable(9);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.date.millisecondsSinceEpoch);
          fbb.addInt64(4, object.dayKey);
          fbb.addInt64(5, object.categoryId);
          fbb.addInt64(6, object.expenseCents);
          fbb.addInt64(7, object.incomeCents);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final dateParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0));
          final dayKeyParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0);
          final categoryIdParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0);
          final expenseCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0);
          final incomeCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 18, 0);
          final object = CategoryStatDaily(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              date: dateParam,
              dayKey: dayKeyParam,
              categoryId: categoryIdParam,
              expenseCents: expenseCentsParam,
              incomeCents: incomeCentsParam);

          return object;
        }),
    CategoryStatMonthly: obx_int.EntityDefinition<CategoryStatMonthly>(
        model: _entities[3],
        toOneRelations: (CategoryStatMonthly object) => [],
        toManyRelations: (CategoryStatMonthly object) => {},
        getId: (CategoryStatMonthly object) => object.id,
        setId: (CategoryStatMonthly object, int id) {
          object.id = id;
        },
        objectToFB: (CategoryStatMonthly object, fb.Builder fbb) {
          fbb.startTable(9);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.month.millisecondsSinceEpoch);
          fbb.addInt64(4, object.monthKey);
          fbb.addInt64(5, object.categoryId);
          fbb.addInt64(6, object.expenseCents);
          fbb.addInt64(7, object.incomeCents);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final monthParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0));
          final monthKeyParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0);
          final categoryIdParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0);
          final expenseCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0);
          final incomeCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 18, 0);
          final object = CategoryStatMonthly(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              month: monthParam,
              monthKey: monthKeyParam,
              categoryId: categoryIdParam,
              expenseCents: expenseCentsParam,
              incomeCents: incomeCentsParam);

          return object;
        }),
    StatDaily: obx_int.EntityDefinition<StatDaily>(
        model: _entities[4],
        toOneRelations: (StatDaily object) => [],
        toManyRelations: (StatDaily object) => {},
        getId: (StatDaily object) => object.id,
        setId: (StatDaily object, int id) {
          object.id = id;
        },
        objectToFB: (StatDaily object, fb.Builder fbb) {
          fbb.startTable(9);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.date.millisecondsSinceEpoch);
          fbb.addInt64(4, object.dayKey);
          fbb.addInt64(5, object.expenseCents);
          fbb.addInt64(6, object.incomeCents);
          fbb.addInt64(7, object.ignoreCents);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final dateParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0));
          final dayKeyParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0);
          final expenseCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0);
          final incomeCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0);
          final ignoreCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 18, 0);
          final object = StatDaily(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              date: dateParam,
              dayKey: dayKeyParam,
              expenseCents: expenseCentsParam,
              incomeCents: incomeCentsParam,
              ignoreCents: ignoreCentsParam);

          return object;
        }),
    StatMonthly: obx_int.EntityDefinition<StatMonthly>(
        model: _entities[5],
        toOneRelations: (StatMonthly object) => [],
        toManyRelations: (StatMonthly object) => {},
        getId: (StatMonthly object) => object.id,
        setId: (StatMonthly object, int id) {
          object.id = id;
        },
        objectToFB: (StatMonthly object, fb.Builder fbb) {
          fbb.startTable(9);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.month.millisecondsSinceEpoch);
          fbb.addInt64(4, object.monthKey);
          fbb.addInt64(5, object.expenseCents);
          fbb.addInt64(6, object.incomeCents);
          fbb.addInt64(7, object.ignoreCents);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final monthParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0));
          final monthKeyParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0);
          final expenseCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0);
          final incomeCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0);
          final ignoreCentsParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 18, 0);
          final object = StatMonthly(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              month: monthParam,
              monthKey: monthKeyParam,
              expenseCents: expenseCentsParam,
              incomeCents: incomeCentsParam,
              ignoreCents: ignoreCentsParam);

          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [ErpCategory] entity fields to define ObjectBox queries.
class ErpCategory_ {
  /// See [ErpCategory.id].
  static final id =
      obx.QueryIntegerProperty<ErpCategory>(_entities[0].properties[0]);

  /// See [ErpCategory.createdAt].
  static final createdAt =
      obx.QueryDateProperty<ErpCategory>(_entities[0].properties[1]);

  /// See [ErpCategory.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<ErpCategory>(_entities[0].properties[2]);

  /// See [ErpCategory.deletedAt].
  static final deletedAt =
      obx.QueryDateProperty<ErpCategory>(_entities[0].properties[3]);

  /// See [ErpCategory.icon].
  static final icon =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[4]);

  /// See [ErpCategory.name].
  static final name =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[5]);

  /// See [ErpCategory.color].
  static final color =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[6]);

  /// See [ErpCategory.objectId].
  static final objectId =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[7]);

  /// see [ErpCategory.children]
  static final children =
      obx.QueryBacklinkToMany<ErpOrder, ErpCategory>(ErpOrder_.parent);
}

/// [ErpOrder] entity fields to define ObjectBox queries.
class ErpOrder_ {
  /// See [ErpOrder.id].
  static final id =
      obx.QueryIntegerProperty<ErpOrder>(_entities[1].properties[0]);

  /// See [ErpOrder.createdAt].
  static final createdAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[1]);

  /// See [ErpOrder.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[2]);

  /// See [ErpOrder.deletedAt].
  static final deletedAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[3]);

  /// See [ErpOrder.triggerAt].
  static final triggerAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[4]);

  /// See [ErpOrder.type].
  static final type =
      obx.QueryIntegerProperty<ErpOrder>(_entities[1].properties[5]);

  /// See [ErpOrder.amountCents].
  static final amountCents =
      obx.QueryIntegerProperty<ErpOrder>(_entities[1].properties[6]);

  /// See [ErpOrder.note].
  static final note =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[7]);

  /// See [ErpOrder.latitude].
  static final latitude =
      obx.QueryDoubleProperty<ErpOrder>(_entities[1].properties[8]);

  /// See [ErpOrder.longitude].
  static final longitude =
      obx.QueryDoubleProperty<ErpOrder>(_entities[1].properties[9]);

  /// See [ErpOrder.parentTable].
  static final parentTable =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[10]);

  /// See [ErpOrder.parentObjectId].
  static final parentObjectId =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[11]);

  /// See [ErpOrder.objectId].
  static final objectId =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[12]);

  /// See [ErpOrder.parent].
  static final parent = obx.QueryRelationToOne<ErpOrder, ErpCategory>(
      _entities[1].properties[13]);
}

/// [CategoryStatDaily] entity fields to define ObjectBox queries.
class CategoryStatDaily_ {
  /// See [CategoryStatDaily.id].
  static final id =
      obx.QueryIntegerProperty<CategoryStatDaily>(_entities[2].properties[0]);

  /// See [CategoryStatDaily.createdAt].
  static final createdAt =
      obx.QueryDateProperty<CategoryStatDaily>(_entities[2].properties[1]);

  /// See [CategoryStatDaily.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<CategoryStatDaily>(_entities[2].properties[2]);

  /// See [CategoryStatDaily.date].
  static final date =
      obx.QueryDateProperty<CategoryStatDaily>(_entities[2].properties[3]);

  /// See [CategoryStatDaily.dayKey].
  static final dayKey =
      obx.QueryIntegerProperty<CategoryStatDaily>(_entities[2].properties[4]);

  /// See [CategoryStatDaily.categoryId].
  static final categoryId =
      obx.QueryIntegerProperty<CategoryStatDaily>(_entities[2].properties[5]);

  /// See [CategoryStatDaily.expenseCents].
  static final expenseCents =
      obx.QueryIntegerProperty<CategoryStatDaily>(_entities[2].properties[6]);

  /// See [CategoryStatDaily.incomeCents].
  static final incomeCents =
      obx.QueryIntegerProperty<CategoryStatDaily>(_entities[2].properties[7]);
}

/// [CategoryStatMonthly] entity fields to define ObjectBox queries.
class CategoryStatMonthly_ {
  /// See [CategoryStatMonthly.id].
  static final id =
      obx.QueryIntegerProperty<CategoryStatMonthly>(_entities[3].properties[0]);

  /// See [CategoryStatMonthly.createdAt].
  static final createdAt =
      obx.QueryDateProperty<CategoryStatMonthly>(_entities[3].properties[1]);

  /// See [CategoryStatMonthly.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<CategoryStatMonthly>(_entities[3].properties[2]);

  /// See [CategoryStatMonthly.month].
  static final month =
      obx.QueryDateProperty<CategoryStatMonthly>(_entities[3].properties[3]);

  /// See [CategoryStatMonthly.monthKey].
  static final monthKey =
      obx.QueryIntegerProperty<CategoryStatMonthly>(_entities[3].properties[4]);

  /// See [CategoryStatMonthly.categoryId].
  static final categoryId =
      obx.QueryIntegerProperty<CategoryStatMonthly>(_entities[3].properties[5]);

  /// See [CategoryStatMonthly.expenseCents].
  static final expenseCents =
      obx.QueryIntegerProperty<CategoryStatMonthly>(_entities[3].properties[6]);

  /// See [CategoryStatMonthly.incomeCents].
  static final incomeCents =
      obx.QueryIntegerProperty<CategoryStatMonthly>(_entities[3].properties[7]);
}

/// [StatDaily] entity fields to define ObjectBox queries.
class StatDaily_ {
  /// See [StatDaily.id].
  static final id =
      obx.QueryIntegerProperty<StatDaily>(_entities[4].properties[0]);

  /// See [StatDaily.createdAt].
  static final createdAt =
      obx.QueryDateProperty<StatDaily>(_entities[4].properties[1]);

  /// See [StatDaily.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<StatDaily>(_entities[4].properties[2]);

  /// See [StatDaily.date].
  static final date =
      obx.QueryDateProperty<StatDaily>(_entities[4].properties[3]);

  /// See [StatDaily.dayKey].
  static final dayKey =
      obx.QueryIntegerProperty<StatDaily>(_entities[4].properties[4]);

  /// See [StatDaily.expenseCents].
  static final expenseCents =
      obx.QueryIntegerProperty<StatDaily>(_entities[4].properties[5]);

  /// See [StatDaily.incomeCents].
  static final incomeCents =
      obx.QueryIntegerProperty<StatDaily>(_entities[4].properties[6]);

  /// See [StatDaily.ignoreCents].
  static final ignoreCents =
      obx.QueryIntegerProperty<StatDaily>(_entities[4].properties[7]);
}

/// [StatMonthly] entity fields to define ObjectBox queries.
class StatMonthly_ {
  /// See [StatMonthly.id].
  static final id =
      obx.QueryIntegerProperty<StatMonthly>(_entities[5].properties[0]);

  /// See [StatMonthly.createdAt].
  static final createdAt =
      obx.QueryDateProperty<StatMonthly>(_entities[5].properties[1]);

  /// See [StatMonthly.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<StatMonthly>(_entities[5].properties[2]);

  /// See [StatMonthly.month].
  static final month =
      obx.QueryDateProperty<StatMonthly>(_entities[5].properties[3]);

  /// See [StatMonthly.monthKey].
  static final monthKey =
      obx.QueryIntegerProperty<StatMonthly>(_entities[5].properties[4]);

  /// See [StatMonthly.expenseCents].
  static final expenseCents =
      obx.QueryIntegerProperty<StatMonthly>(_entities[5].properties[5]);

  /// See [StatMonthly.incomeCents].
  static final incomeCents =
      obx.QueryIntegerProperty<StatMonthly>(_entities[5].properties[6]);

  /// See [StatMonthly.ignoreCents].
  static final ignoreCents =
      obx.QueryIntegerProperty<StatMonthly>(_entities[5].properties[7]);
}
