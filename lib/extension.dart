import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';

import 'app/models/erp_category.dart';
import 'app/models/erp_order.dart';
import 'app/utils/category_colors.dart';
import 'app/utils/category_icons.dart';
import 'enums.dart';

extension GetExtension on GetInterface {
  Future<void> showLoading() async {
    await Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Future<void> showAlert(String message) async {
    await Get.dialog(
      AlertDialog(
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

extension WidgetX on Widget {
  // 可以把任何 Widget 使用 Dialog 形式呈現
  Future<T?> dialog<T>({
    bool barrierDismissible = true,
    EdgeInsets insetPadding = EdgeInsets.zero,
  }) {
    return Get.dialog<T>(
      Dialog(
        insetPadding: insetPadding,
        backgroundColor: Colors.transparent,
        elevation: 0.0,
        child: SizedBox(
          // width: 300.dw,
          child: this,
        ),
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  // 可以把任何 Widget 使用 Sheet 形式呈現
  Future<T?> sheet<T>({
    final bool isDismissible = true,
    final bool enableDrag = false,
    final bool isScrollControlled = true,
    final bool ignoreSafeArea = false,
  }) {
    return Get.bottomSheet<T>(
      this,
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      ignoreSafeArea: ignoreSafeArea,
      isDismissible: isDismissible,
      enterBottomSheetDuration: 200.milliseconds,
      exitBottomSheetDuration: 200.milliseconds,
    );
  }
}

/// 通用 UI 計算擴展
extension BuildContextX on BuildContext {
  /// 統一計算為了避開 FloatingActionButton 的底部 padding。
  ///
  /// - contentPadding: 內容與 FAB 保持的額外距離，預設 16
  /// - fabHeight: FAB 本身高度，預設 56
  /// - includeSafeArea: 是否把安全區域（如 iPhone 底部）一起算進來
  double fabBottomPadding({
    double contentPadding = 16.0,
    double fabHeight = 56.0,
    bool includeSafeArea = false,
  }) {
    final safe = includeSafeArea ? MediaQuery.of(this).padding.bottom : 0.0;
    return contentPadding + kFloatingActionButtonMargin + fabHeight + safe;
  }
}

extension ThemeModeX on ThemeMode {
  String get display {
    switch (this) {
      case ThemeMode.light:
        return 'theme_light'.tr;
      case ThemeMode.dark:
        return 'theme_dark'.tr;
      case ThemeMode.system:
        return 'theme_system'.tr;
    }
  }
}

extension LocaleX on Locale {
  String get display {
    switch ('${languageCode}_$countryCode') {
      case 'en_US':
        return 'language_english'.tr;
      case 'zh_TW':
        return 'language_chinese'.tr;
      default:
        return 'language_chinese'.tr;
    }
  }

  String get flagEmoji {
    switch ('${languageCode}_$countryCode') {
      case 'en_US':
        return '🇺🇸';
      case 'zh_TW':
        return '🇹🇼';
      default:
        return '🇹🇼';
    }
  }
}

extension GetStorageX on GetStorage {
  Stream<String> watch([String? key]) async* {
    late final StreamController<String> streamController;
    late final VoidCallback disposeListen;

    try {
      streamController = StreamController<String>();

      if (key != null && key.isNotEmpty) {
        disposeListen = listenKey(key, (value) {
          if (!streamController.isClosed) {
            streamController.add(value ?? '');
          }
        });
      } else {
        disposeListen = listen(() {
          if (!streamController.isClosed) {
            streamController.add('');
          }
        });
      }

      // 發出初始值
      yield key ?? '';

      // 持續監聽變更
      await for (final value in streamController.stream) {
        yield value;
      }
    } finally {
      // 確保資源被正確釋放
      disposeListen.call();
      if (!streamController.isClosed) {
        streamController.close();
      }
    }
  }
}

extension ErpCategoryX on ErpCategory {
  // Helper method to get the color as a Color object
  Color getColor() {
    if (color == null || color!.isEmpty) {
      return CategoryColors.availableColors.first;
    }

    try {
      return Color(int.parse(color!.replaceFirst('#', '0xFF')));
    } catch (e) {
      // If parsing fails, return default color
      return CategoryColors.availableColors.first;
    }
  }

  void setColor(Color color) {
    this.color = '#${color.value.toRadixString(16).substring(2)}';
  }

  // Helper method to get the icon as an IconData object
  IconData getIcon() => CategoryIcons.getIcon(icon);

  void setIcon(IconData icon) {
    this.icon = CategoryIcons.getIconName(icon);
  }
}

/// 金額格式化擴展
extension DoubleX on double {
  /// 格式化金額顯示，添加千分位逗號
  String get formattedAmount {
    final formatter = NumberFormat('#,##0.##', 'zh_TW');
    return formatter.format(this.abs());
  }

  /// 格式化為帶正負號的金額
  String get formattedAmountWithSign {
    final isPositive = this >= 0;
    final sign = isPositive ? '+' : '-';
    return '$sign${this.formattedAmount}';
  }
}

/// 日期時間格式化擴展
extension DateTimeX on DateTime {
  /// 獲取相對時間顯示（今天、昨天等）
  String get relativeDay {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final thisDate = DateTime(this.year, this.month, this.day);

    if (thisDate == today) {
      return 'transactions_today'.tr;
    } else if (thisDate == yesterday) {
      return 'transactions_yesterday'.tr;
    } else {
      return DateFormat('MM/dd').format(this);
    }
  }

  /// 格式化時間顯示
  String get formattedTime {
    return DateFormat('HH:mm').format(this);
  }

  /// 格式化日期顯示
  String get formattedDate {
    return DateFormat('yyyy/MM/dd').format(this);
  }
}

/// ErpOrder 擴展
extension ErpOrderX on ErpOrder {
  /// 是否為收入
  bool get isIncome => transactionType == TransactionType.income;

  /// 是否為支出
  bool get isExpense => transactionType == TransactionType.expense;

  /// 格式化金額顯示
  String get formattedAmount {
    final formatter = NumberFormat('#,##0.##', 'zh_TW');
    return formatter.format(amount.abs());
  }

  /// 格式化帶正負號的金額
  String get formattedAmountWithSign {
    final sign = isExpense ? '-' : '';
    return '$sign$formattedAmount';
  }

  /// 獲取金額顏色
  Color get amountColor {
    if (amount == 0) return Colors.grey;
    return isIncome ? Colors.green : Colors.red;
  }

  TransactionType get transactionType {
    if (type == null) return TransactionType.ignore;
    return TransactionType.values[type!];
  }

  set transactionType(TransactionType type) {
    this.type = type.index;
  }

  LatLng get latLng {
    return LatLng(latitude ?? 0.0, longitude ?? 0.0);
  }

  set latLng(LatLng latLng) {
    latitude = latLng.latitude;
    longitude = latLng.longitude;
  }

  /// 顯示用金額字串（根據 amount 動態計算）
  String get amountText {
    final v = amount;
    if (v == null) return '0';
    // 轉為不帶多餘 0 的可讀格式
    if (v % 1 == 0) {
      return v.toInt().toString();
    }
    var s = v.toString();
    if (s.contains('.')) {
      // 去除小數末尾多餘的 0 與獨立的小數點
      s = s.replaceFirst(RegExp(r'0+$'), '');
      s = s.replaceFirst(RegExp(r'\.$'), '');
    }
    return s;
  }

  /// 便利屬性：以元為單位讀取金額（從 amountCents 換算）
  double get amount => (amountCents ?? 0) * 0.01;

  /// 便利屬性：以元為單位設定金額（寫回 amountCents，四捨五入到分）
  set amount(double? value) {
    amountCents = value == null ? 0 : (value * 100.0).round();
  }
}

/// 通用數值轉貨幣字串（有小數才顯示小數位）
extension NumCurrencyX on num {
  /// 轉為貨幣格式：
  /// - 依是否有小數顯示 `#,##0` 或 `#,##0.##`
  /// - 可傳入 `symbol`（例如 `$`、`NT$` 等），預設不帶符號
  /// - 預設地區為 `zh_TW`
  String toCurrency({String? symbol, String locale = 'zh_TW'}) {
    final hasFraction = this % 1 != 0;
    final pattern = hasFraction ? '#,##0.##' : '#,##0';
    final formatted = NumberFormat(pattern, locale).format(this);
    if (symbol != null && symbol.isNotEmpty) {
      return '$symbol$formatted';
    }
    return formatted;
  }
}

extension ContextThemeX on BuildContext {
  /// 半透明卡片背景色，預設 alpha = 0.3
  Color cardOverlay([double alpha = 0.3]) {
    return Theme.of(this).cardColor.withValues(alpha: alpha);
  }

  /// 半透明卡片邊框色，預設 alpha = 0.1
  Color cardOverlayBorder([double alpha = 0.1]) {
    return Theme.of(this).colorScheme.onSurface.withValues(alpha: alpha);
  }
}