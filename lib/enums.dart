import 'package:flutter/material.dart';

enum Boxes {
  settings,
  categories,
  orders,
}

enum Button {
  cancel,
  confirm,
}

extension ButtonX on Button {
  String get display {
    switch (this) {
      case Button.confirm:
        return '確認';
      case Button.cancel:
        return '取消';
    }
  }

  bool get value {
    switch (this) {
      case Button.confirm:
        return true;
      case Button.cancel:
        return false;
    }
  }
}

enum ActionType {
  none,
  add,
  edit,
  delete,
  read,
  put,
}

extension ActionX on ActionType {
  String get display {
    switch (this) {
      case ActionType.add:
        return '新增';
      case ActionType.edit:
        return '編輯';
      case ActionType.delete:
        return '刪除';
      case ActionType.read:
        return '查看';
      case ActionType.put:
        return '放置';
      case ActionType.none:
        return '未知';
    }
  }

  bool get isAdd => this == ActionType.add;
  bool get isEdit => this == ActionType.edit;
  bool get isDelete => this == ActionType.delete;
}

enum TransactionType {
  expense,
  income,
  ignore,
}

extension TransactionTypeX on TransactionType {
  String get displayName {
    switch (this) {
      case TransactionType.expense:
        return '支出';
      case TransactionType.income:
        return '收入';
      case TransactionType.ignore:
        return '不計';
    }
  }

  IconData get icon {
    switch (this) {
      case TransactionType.expense:
        return Icons.arrow_upward;
      case TransactionType.income:
        return Icons.arrow_downward;
      case TransactionType.ignore:
        return Icons.remove;
    }
  }
}
