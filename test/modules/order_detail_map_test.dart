import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_map/flutter_map.dart';

import 'package:pocket_trac/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:pocket_trac/app/modules/order_detail/views/order_detail_view.dart';
import 'package:pocket_trac/app/repositories/category_repository.dart';
import 'package:pocket_trac/app/repositories/order_repository.dart';
import 'package:pocket_trac/constants.dart';

class MockCategoryRepository extends GetxService implements CategoryRepository {
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

class MockOrderRepository extends GetxService implements OrderRepository {
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  group('OrderDetailView 地圖功能測試', () {
    late OrderDetailController controller;

    setUp(() {
      Get.testMode = true;
      Get.put<CategoryRepository>(MockCategoryRepository());
      Get.put<OrderRepository>(MockOrderRepository());
      controller = OrderDetailController(
        categoryRepository: Get.find<CategoryRepository>(),
        orderRepository: Get.find<OrderRepository>(),
      );
      Get.put(controller);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('地圖應該禁用交互功能', (WidgetTester tester) async {
      // 設置地理位置資料
      controller.draft.latitude = Constants.taipeiLatitude;
      controller.draft.longitude = Constants.taipeiLongitude;

      await tester.pumpWidget(
        GetMaterialApp(
          home: OrderDetailView(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找 FlutterMap 組件
      final mapFinder = find.byType(FlutterMap);
      expect(mapFinder, findsOneWidget);

      // 獲取 FlutterMap widget
      final FlutterMap mapWidget = tester.widget(mapFinder);

      // 驗證交互選項設置為禁用所有交互
      expect(mapWidget.options.interactionOptions.flags,
          equals(InteractiveFlag.none));
    });

    testWidgets('地圖標記應該顯示在正確位置', (WidgetTester tester) async {
      const testLat = Constants.taipeiLatitude;
      const testLng = Constants.taipeiLongitude;

      // 設置地理位置資料
      controller.draft.latitude = testLat;
      controller.draft.longitude = testLng;

      await tester.pumpWidget(
        GetMaterialApp(
          home: OrderDetailView(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找 MarkerLayer 組件
      final markerLayerFinder = find.byType(MarkerLayer);
      expect(markerLayerFinder, findsOneWidget);

      // 獲取 MarkerLayer widget
      final MarkerLayer markerLayer = tester.widget(markerLayerFinder);

      // 驗證標記位置
      expect(markerLayer.markers.length, equals(1));
      final marker = markerLayer.markers.first;
      expect(marker.point.latitude, equals(testLat));
      expect(marker.point.longitude, equals(testLng));
    });
  });
}
